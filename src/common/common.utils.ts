import { format, parseISO } from "date-fns";

export const DEFAULT_DATE_FORMAT = "yyyy-MM-dd";

export const formatDate = (date?: string | Date | undefined | null) => {
  if (date && typeof date === "object" && date instanceof Date) {
    return format(parseISO(date.toISOString()), DEFAULT_DATE_FORMAT);
  }

  const isoDate = date ? parseISO(date) : parseISO(new Date().toISOString());
  return format(isoDate, DEFAULT_DATE_FORMAT);
};

export const generateTimeOptions = () => {
  const hours = Array.from({ length: 18 }, (_, i) => (i + 6) % 24);

  return hours.flatMap(hour => {
    const hour24 = hour.toString().padStart(2, "0");
    const hour12 = (((hour + 11) % 12) + 1).toString();
    const period = hour < 12 ? "am" : "pm";
    const value = `${hour24}:00`;
    const label = `${hour12}:00${period}`;

    return { label, value };
  });
};
