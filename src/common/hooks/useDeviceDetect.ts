"use client";

import { useEffect, useState } from "react";

// Default values for server-side rendering
const defaultDeviceState = {
  isMobile: false,
  isMobileOnly: false,
};

export function useDeviceDetect() {
  const [deviceState, setDeviceState] = useState(defaultDeviceState);

  useEffect(() => {
    // Only import and use react-device-detect on the client side
    const loadDeviceDetect = async () => {
      try {
        const { isMobile, isMobileOnly } = await import("react-device-detect");
        setDeviceState({ isMobile, isMobileOnly });
      } catch (error) {
        console.error("Error loading device detection:", error);
      }
    };

    loadDeviceDetect();
  }, []);

  return deviceState;
}
