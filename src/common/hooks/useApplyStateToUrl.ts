"use-client";

import { useCallback } from "react";

import { useRouter } from "next/navigation";

export const useApplyStateToUrl = <T>() => {
  const router = useRouter();

  return useCallback(
    (states: Record<string, T>) => {
      const searchParams = new URLSearchParams(window.location.search);
      Object.entries(states).forEach(([key, value]) => {
        if (!value) {
          searchParams.delete(key);
        } else {
          searchParams.set(key, `${value}`);
        }
      });

      router.push(`?${searchParams.toString()}`);
    },
    [router]
  );
};
