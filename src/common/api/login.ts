"use server";
import { signOut } from "@/components/custom/login-auth/auth-provider";
import { BASE_API_URL } from "./constants";

export interface LoginUserResponse {
  success: string;
  message: string;
  token: string;
  university_id: string;
  first_name?: string;
  last_name?: string;
}

export const login = async ({
  email,
  password,
}: {
  email: string;
  password: string;
}): Promise<LoginUserResponse> => {
  try {
    const response = await fetch(`${BASE_API_URL}/auth/login/legacy`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ email, password }),
    });
    return await response.json();
  } catch (error) {
    signOut();
    throw new Error("Unable to login user");
  }
};
