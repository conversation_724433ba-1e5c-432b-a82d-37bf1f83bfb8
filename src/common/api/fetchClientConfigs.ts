"use server";

import { ALL_OPTION } from "@/components/custom/activity-filters";
import { BASE_API_URL } from "./constants";

type DataItem = {
  id: string;
  [key: string]: string;
};

const generateOptions = (data: DataItem[], id: string, labelKey: string) => {
  return data.map(item => ({
    ...item,
    id,
    label: item[labelKey],
    value: String(item.id),
  }));
};

export const fetchClientConfigs = async (orgId: string) => {
  // If no orgId is provided, return default empty values
  if (!orgId) {
    return {
      facilitiesOptions: [
        { id: "gym_id", label: "All Locations", value: ALL_OPTION },
      ],
      categoriesOptions: [
        { id: "room_id", label: "All Activities", value: ALL_OPTION },
      ],
      ptCategoriesOptions: [
        { id: "category_id", label: "All Categories", value: ALL_OPTION },
      ],
      ptSessionsOptions: [
        { id: "session_id", label: "All Types", value: ALL_OPTION },
      ],
      equipmentCategoriesOptions: [
        { id: "room_id", label: "All Activities", value: ALL_OPTION },
      ],
      specialtiesOptions: [],
      configs: {},
      appUrls: {},
      clientName: "",
      embedsConfigs: {},
      scheduleUrl: "",
      sgtPurchaseUrl: "",
      clientLogo: "",
      clientPdfHeader: "",
      clientBackground: "",
    };
  }

  try {
    const rec = await fetch(`${BASE_API_URL}/client/info?uni_id=${orgId}`, {
      cache: "no-cache",
    });

    if (!rec.ok) {
      throw new Error(`API returned status ${rec.status}`);
    }

    const response = await rec.json();

    const facilitiesOptions = generateOptions(
      response?.data?.facilities,
      "gym_id",
      "name"
    );
    const categoriesOptions = generateOptions(
      response?.data?.class_categories,
      "room_id",
      "category_name"
    );
    const specialtiesOptions = generateOptions(
      response?.data?.trainer_specialties,
      "specialty_id",
      "name"
    );

    const ptCategories = generateOptions(
      response?.data?.pt_categories,
      "category_id",
      "name"
    );

    const ptSessions = generateOptions(
      response?.data?.pt_sessions,
      "session_id",
      "name"
    );
    const equipmentCategories = generateOptions(
      response?.data?.equipment_types,
      "room_id",
      "name"
    );

    return {
      facilitiesOptions: [
        {
          id: "gym_id",
          label: "All Locations",
          value: ALL_OPTION,
        },
        ...facilitiesOptions,
      ],

      categoriesOptions: [
        { id: "room_id", label: "All Activities", value: ALL_OPTION },
        ...categoriesOptions,
      ],
      ptCategoriesOptions: [
        { id: "category_id", label: "All Categories", value: ALL_OPTION },
        ...ptCategories,
      ],
      ptSessionsOptions: [
        { id: "session_id", label: "All Types", value: ALL_OPTION },
        ...ptSessions,
      ],

      equipmentCategoriesOptions: [
        { id: "room_id", label: "All Activities", value: ALL_OPTION },
        ...equipmentCategories,
      ],
      specialtiesOptions,
      configs: response?.data?.configs,
      appUrls: response?.data?.app_urls,
      clientName: response?.data?.client_name,
      embedsConfigs: response?.data?.configs?.embed,
      scheduleUrl: response?.data?.configs?.embed?.equipment_schedule_url,
      sgtPurchaseUrl: response?.data?.legacy_app_configs?.sgt_purchase_url,
      clientLogo: response?.data?.client_logo,
      clientPdfHeader: response?.data?.client_disclaimers?.pdf_header,
      clientBackground: response?.data?.client_background,
    };
  } catch (err) {
    throw new Error("Could not fetch configs");
  }
};
