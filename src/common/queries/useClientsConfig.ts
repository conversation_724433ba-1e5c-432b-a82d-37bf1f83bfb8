// "use client";

// import { fetchClientConfigs } from "@/common/api/fetchClientConfigs";
// import { useQuery } from "@tanstack/react-query";
// import { useParams } from "next/navigation";
// import { ALL_OPTION } from "../../components/custom/activity-filters";

// const CLIENT_CONFIG_QUERY_KEY = "CLIENT_CONFIG_QUERY_KEY";

// export const useClientsConfig = () => {
//   const params = useParams();
//   return useQuery({
//     queryKey: [CLIENT_CONFIG_QUERY_KEY, params?.orgId],
//     queryFn: () => fetchClientConfigs(params?.orgId as string),
//     select: ({ data }) => {
//       const facilitiesOptions = data?.facilities?.map(facility => ({
//         id: "gym_id",
//         label: facility?.name,
//         value: String(facility?.id),
//       }));

//       const categoriesOptions = data?.class_categories?.map(category => ({
//         id: "room_id",
//         label: category?.category_name,
//         value: String(category?.id),
//       }));

//       return {
//         facilitiesOptions: [
//           {
//             id: "gym_id",
//             label: "All Locations",
//             value: ALL_OPTION,
//           },
//           ...facilitiesOptions,
//         ],

//         categoriesOptions: [
//           { id: "room_id", label: "All Activities", value: ALL_OPTION },
//           ...categoriesOptions,
//         ],
//         configs: data?.configs,
//         appUrls: data?.app_urls,
//       };
//     },
//   });
// };
