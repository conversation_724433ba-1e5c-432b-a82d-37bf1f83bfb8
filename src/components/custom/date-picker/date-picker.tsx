"use client";

import { useApplyStateToUrl } from "../../../common/hooks/useApplyStateToUrl";

import { useSearchParams } from "next/navigation";

import { styled } from "twin.macro";
import { LoginLogoutButton } from "../login-logout-button";
import { useStoreValue } from "@/app/StoreContext";
import { WeeklyCalendar } from "../custom-date-picker/weekly-datepicker";

const Wrapper = styled.div<{ color?: string }>`
  color: ${({ color }) => color};
  margin-bottom: 20px;
`;

export const DateFormatPicker = () => {
  const applyToUrl = useApplyStateToUrl();

  const searchParams = useSearchParams();

  const { state: embedConfigs } = useStoreValue(state => state.configs);

  return (
    <div
      style={{ font: embedConfigs?.fontName }}
      className='container rounded-b-none rounded-t-lg border-2 border-white-300 !bg-white flex flex-col mt-4'
    >
      <LoginLogoutButton />
      <Wrapper color={embedConfigs?.accent_color}>
        <WeeklyCalendar
          initialDate={searchParams.get("date") || ""}
          onChange={date => applyToUrl({ date })}
        />
      </Wrapper>
    </div>
  );
};
