/* eslint-disable @typescript-eslint/ban-ts-comment */
// Dynamically import html2pdf to avoid SSR issues
import { PDFOptions } from "./types";
import { forEach, uniq } from "lodash/fp";
import { ClassDetailsResponse } from "@/app/classes/[orgId]/types";
let html2pdf: any;

// Only import html2pdf on the client side
if (typeof window !== "undefined") {
  // @ts-expect-error html2pdf does not support types
  import("html3pdf").then(module => {
    html2pdf = module.default;
  });
}

export const defaultPDFOptions: PDFOptions = {
  margin: 0.4,
  filename: "class-schedule.pdf",
  image: {
    type: "jpeg",
    quality: 0.98,
  },
  html2canvas: {
    scale: 5,
    dpi: 192,
    letterRendering: true,
  },
  jsPDF: {
    unit: "in",
    format: "letter",
    orientation: "landscape",
  },
  pagebreak: { mode: "avoid-all", before: "#page2el", elementType: "img" },
  enableLink: true,
};

export const generatePDF = async (
  element: HTMLElement,
  options: Partial<PDFOptions> = {}
): Promise<void> => {
  // Check if we're on the client side
  if (typeof window === "undefined") {
    console.error("PDF generation is only available in the browser");
    return;
  }

  const mergedOptions = {
    ...defaultPDFOptions,
    ...options,
  };

  try {
    // Dynamically import html2pdf if it's not already loaded
    if (!html2pdf) {
      // @ts-expect-error html2pdf does not support types
      const module = await import("html3pdf");
      html2pdf = module.default;
    }

    await html2pdf().set(mergedOptions).from(element).save();
  } catch (error) {
    console.error("Error generating PDF:", error);
    throw error;
  }
};

export const groupClassesByDaysOfWeek = (classes: ClassDetailsResponse[]) => {
  const groups = {
    mon: [],
    tue: [],
    wed: [],
    thu: [],
    fri: [],
    sat: [],
    sun: [],
  };

  // Process each class
  forEach(classItem => {
    const uniqueDays = uniq(
      classItem?.days_of_week?.map(day => day?.toLowerCase())
    );

    // Add class to each unique day's array
    forEach(day => {
      if (day in groups) {
        // @ts-expect-error test error
        groups?.[day]?.push(classItem);
      }
    }, uniqueDays);
  }, classes);

  return groups;
};
