import { Calendar } from "lucide-react";
import { useStoreValue } from "@/app/StoreContext";
import { cn } from "@/lib/utils";
import { useCancelReservation } from "../../app/classes/[orgId]/mutations/useCancelReservation";
import { ActionButton } from "@/components/custom/action-button";

export const CancelButton = ({
  id,
  onClick,
  label = "Cancel Reservation",
  className,
  onSuccess,
}: {
  id?: number;
  onClick?: () => void;
  label?: string;
  className?: string;
  onSuccess?: () => void;
}) => {
  const { dispatch } = useStoreValue();

  const { mutate: handleCancellation, isPending } = useCancelReservation(() => {
    dispatch(() => ({ cancelReservation: null }));
    onSuccess?.();
  });

  return (
    <ActionButton
      icon={<Calendar />}
      className={cn("!border-red-500 !text-red-500", className)}
      isLoading={isPending}
      onClick={() => (onClick ? onClick() : handleCancellation(Number(id)))}
    >
      {label}
    </ActionButton>
  );
};
