import { format } from "date-fns";
import { useTime } from "react-timer-hook";

export const TimeClock = ({
  color,
  font,
}: {
  color?: string;
  font?: string;
}) => {
  const { seconds, minutes, hours, ampm } = useTime({ format: "12-hour" });

  const hours12 = hours === 0 && ampm === "pm" ? 12 : hours;

  const formatHour = hours12 < 10 ? `0${hours12}` : hours12;
  const formatMinutes = minutes < 10 ? `0${minutes}` : minutes;
  const formatSeconds = seconds < 10 ? `0${seconds}` : seconds;

  return (
    <div style={{ color, fontFamily: font }}>
      <div className='relative'>
        <span className='text-9xl'>{`${formatHour}:${formatMinutes}`}</span>
        <span className='text-3xl absolute top-4'>{formatSeconds}</span>
        <span className='text-3xl'>{ampm.toUpperCase()}</span>
      </div>

      <p className='text-center text-4xl'>
        {format(new Date(), "EEEE, MMMM d")}
      </p>
    </div>
  );
};
