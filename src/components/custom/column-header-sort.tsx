import { MoveDown, MoveUp } from "lucide-react";

export const SortArrow = ({
  onClick,
  sortDirection,
  columnName,
  className,
}: {
  sortDirection?: string | boolean;
  onClick: () => void;
  columnName: string;
  className?: string;
}) => {
  return (
    <div
      className={`flex flex-row items-center cursor-pointer ${className}`}
      onClick={onClick}
      onKeyUp={onClick}
    >
      {columnName}
      {sortDirection === "asc" ? (
        <MoveDown className='ml-2 h-4 w-4' />
      ) : (
        <MoveUp className='ml-2 h-4 w-4' />
      )}
    </div>
  );
};
