import { cloneElement, ReactElement } from "react";
import { Button } from "../ui/button";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

export const ActionButton = ({
  icon,
  children,
  className,
  onClick,
  isLoading,
  isDisabled,
  variant = "outline",
  style,
  type = "button",
}: {
  onClick?: () => void;
  icon?: ReactElement;
  children: React.ReactNode;
  className?: string;
  isLoading?: boolean;
  isDisabled?: boolean;
  variant?:
    | "link"
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost";
  style?: Record<string, string | number>;
  type?: "button" | "submit" | "reset";
}) => {
  return (
    <Button
      disabled={isDisabled || isLoading}
      onClick={e => {
        e.stopPropagation();
        onClick?.();
      }}
      variant={variant}
      className={cn(
        "w-full text-blue-500 text-xs font-bold whitespace-nowrap",
        className
      )}
      style={style}
      type={type}
    >
      {isLoading ? (
        <Loader2 className='mr-2 h-4 w-4 animate-spin' />
      ) : (
        <span className='hidden lg:flex md:flex'>
          {icon && cloneElement(icon, { size: 16 })}
        </span>
      )}

      <span className='lg:pl-1 md:pl-1 text-[10px] sm:text-xs'>{children}</span>
    </Button>
  );
};
