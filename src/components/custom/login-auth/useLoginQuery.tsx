"use client";

import { BASE_API_URL_CLIENT } from "@/app/classes/[orgId]/actions/constant";
import { useMutation } from "@tanstack/react-query";
import { signOut } from "./auth-provider";
import { toast } from "sonner";

export interface LoginUserResponse {
  success: string;
  message: string;
  token: string;
  university_id: string;
  first_name?: string;
  last_name?: string;
  name?: string;
}

export const login = async ({
  email,
  password,
}: {
  email: string;
  password: string;
  orgId?: string;
}): Promise<LoginUserResponse> => {
  try {
    const response = await fetch(`${BASE_API_URL_CLIENT}/auth/login/legacy`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ email, password }),
    });

    if (response.ok) {
      return await response.json();
    }

    throw new Error("Unable to login user");
  } catch (error) {
    signOut();
    throw new Error("Unable to login user");
  }
};

export const useLoginQuery = (onSuccess?: (data?: LoginUserResponse) => void) =>
  useMutation({
    mutationFn: login,
    onSuccess: (data, reqData) => {
      //Remember: This is a workaround to support legacy login methods
      if (data?.university_id != reqData?.orgId) {
        toast.error(
          "Sorry, you do not not have account associated with this organization"
        );
        throw new Error(
          "Sorry, you do not not have account associated with this organization"
        );
      }
      onSuccess?.(data);
    },
  });
