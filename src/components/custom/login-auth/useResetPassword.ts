"use client";

import { BASE_API_URL_CLIENT } from "@/app/classes/[orgId]/actions/constant";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { useSession } from "./auth-provider";
import { useParams } from "next/navigation";

export const resetPassword = async (data: unknown) => {
  const headers: { "Content-Type": string; authorization?: string } = {
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(
      `${BASE_API_URL_CLIENT}/auth/password/reset/legacy`,
      {
        headers,
        method: "POST",
        body: JSON.stringify(data),
      }
    );

    return await response.json();
  } catch (err) {
    throw new Error("Sorry there is a problem resetting the password");
  }
};

export const useResetPassword = (onSuccess?: () => void) => {
  const params = useParams();

  const { signIn } = useSession();
  return useMutation({
    mutationFn: resetPassword,

    onSuccess: async (
      data,
      requestData: { email?: string; password?: string }
    ) => {
      if (data.success) {
        toast.success(data?.message);
        await signIn({
          email: requestData.email as string,
          password: requestData.password as string,
          orgId: params?.orgId as string,
        });
        return onSuccess?.();
      }

      return toast.error(data?.message);
    },

    onError: e => {
      return toast.error(e?.message);
    },
  });
};
