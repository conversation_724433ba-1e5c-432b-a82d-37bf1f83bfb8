"use client";

import { BASE_API_URL_CLIENT } from "@/app/classes/[orgId]/actions/constant";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";

export const checkEmail = async (email: string) => {
  const headers: { "Content-Type": string; authorization?: string } = {
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(
      `${BASE_API_URL_CLIENT}/auth/request_password_reset`,
      {
        headers,
        method: "POST",
        body: JSON.stringify({ email }),
      }
    );

    return await response.json();
  } catch (err) {
    throw new Error("Sorry there is a problem resetting the password");
  }
};

export const useCheckEmail = (onSuccess?: () => void) => {
  return useMutation({
    mutationFn: checkEmail,

    onSuccess: async data => {
      if (data.success) {
        onSuccess?.();
        return toast.success(data?.message);
      }
      return toast.error(data?.message);
    },

    onError: e => {
      return toast.error(e?.message);
    },
  });
};
