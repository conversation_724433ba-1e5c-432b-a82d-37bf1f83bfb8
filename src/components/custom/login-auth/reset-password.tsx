"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

import { MdAlternateEmail, MdOutlinePin } from "react-icons/md";
import { IoLockClosedOutline } from "react-icons/io5";
import { useCheckEmail } from "./useCheckEmail";
import { useResetPassword } from "./useResetPassword";
import { useStoreValue } from "@/app/StoreContext";
import { Loader2 } from "lucide-react";
import { useEffect } from "react";

export const ResetPasswordSchema = z
  .object({
    email: z.string().min(2, {
      message: "Email is required",
    }),
    password: z.string().min(1, {
      message: "Password is required",
    }),
    confirm_password: z.string().min(1, {
      message: "Confirm password is required",
    }),
    pin: z.string().min(1, {
      message: "Pin is required",
    }),
  })
  .refine(data => data.password === data.confirm_password, {
    message: "Passwords don't match",
    path: ["confirm_password"],
  });

export function ResetPassword() {
  const form = useForm<z.infer<typeof ResetPasswordSchema>>({
    resolver: zodResolver(ResetPasswordSchema),
    defaultValues: {
      email: "",
      password: "",
      confirm_password: "",
    },
  });

  const {
    mutate: checkEmail,
    data: emailData,
    isPending,
  } = useCheckEmail(() =>
    form.reset({
      email: form.getValues().email,
      password: "",
      confirm_password: "",
      pin: "",
    })
  );

  const { dispatch } = useStoreValue();

  const { mutate: resetPassword, isPending: isLoading } = useResetPassword(() =>
    dispatch(() => ({ shouldShowLoginModal: false }))
  );

  const onSubmit = (formData: z.infer<typeof ResetPasswordSchema>) => {
    if (emailData) {
      return resetPassword(formData);
    }
    return checkEmail(formData.email);
  };

  useEffect(() => {
    if (!emailData?.success) {
      form.setValue("confirm_password", " ");
      form.setValue("password", " ");
      form.setValue("pin", " ");
    }
  }, [emailData, form]);

  const errors = form.formState.errors;

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className=' grid gap-4 pt-8'>
        {errors?.root && (
          <FormMessage className='text-center'>
            {errors?.root?.message}
          </FormMessage>
        )}
        <FormField
          control={form.control}
          name='email'
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className='relative col-span-3'>
                  <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                    <MdAlternateEmail className='text-gray-500' />
                  </div>
                  <Input
                    className='pl-8 text-base'
                    placeholder='Enter Email Address'
                    type='email'
                    {...field}
                    disabled={emailData?.success}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        {emailData?.success && (
          <>
            <FormField
              control={form.control}
              name='pin'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <div className='relative col-span-3'>
                      <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                        <MdOutlinePin className='text-gray-500 h-5' />
                      </div>
                      <Input
                        className='pl-8 text-base'
                        placeholder='Enter Pin'
                        {...field}
                        type='number'
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name='password'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <div className='relative col-span-3'>
                      <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                        <IoLockClosedOutline className='text-gray-500' />
                      </div>
                      <Input
                        className='pl-8 text-base'
                        placeholder='Password'
                        type='password'
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name='confirm_password'
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <div className='relative col-span-3'>
                      <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                        <IoLockClosedOutline className='text-gray-500' />
                      </div>
                      <Input
                        className='pl-8 text-base'
                        placeholder='Confirm password'
                        type='password'
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </>
        )}
        <Button type='submit' disabled={isPending || isLoading}>
          {(isLoading || isPending) && (
            <Loader2 className='mr-2 h-4 w-4 animate-spin' />
          )}
          {emailData ? "Reset Password" : "Request Pin"}
        </Button>
      </form>
    </Form>
  );
}
