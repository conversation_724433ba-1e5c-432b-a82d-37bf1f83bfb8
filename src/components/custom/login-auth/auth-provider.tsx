"use client";

import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from "react";
import { LoginUserResponse, useLoginQuery } from "./useLoginQuery";
import { useAppStore } from "@/app/StoreContext";
import { StorageService } from "@/lib/storage-service";
import { toast } from "sonner";

/**
 * Token key for storage
 */
const UPACE_TOKEN = "__upace";

/**
 * Session expiration time in milliseconds (24 hours)
 */
const SESSION_EXPIRATION = 24 * 60 * 60 * 1000;

/**
 * User session type definition
 */
type UserSession = {
  signIn: ({
    email,
    password,
    orgId,
  }: {
    email: string;
    password: string;
    orgId?: string;
  }) => Promise<void>;
  data?: LoginUserResponse | undefined;
  signOut: () => Promise<void>;
  isError?: boolean;
  isLoading: boolean;
};

/**
 * Default context value
 */
const CurrentUserContext = createContext<UserSession>({
  signIn: async () => {},
  signOut: async () => {},
  isLoading: false,
});

/**
 * Hook to access the current user session
 */
export const useSession = () => useContext(CurrentUserContext);

/**
 * Get the current session from storage
 */
export const getSession = async (): Promise<LoginUserResponse | undefined> => {
  return StorageService.getItem<LoginUserResponse>(UPACE_TOKEN);
};

/**
 * Sign out the current user
 */
export const signOut = async (): Promise<void> => {
  await StorageService.removeItem(UPACE_TOKEN);
};

/**
 * Authentication context provider
 */
export const AuthContextProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const [userData, setUserData] = useState<LoginUserResponse | undefined>(
    undefined
  );
  const [isLoading, setIsLoading] = useState(true);
  const setLoginModal = useAppStore(state => state.setLoginModal);

  // Load user data from storage on mount
  useEffect(() => {
    const loadUserData = async () => {
      try {
        const data = await getSession();
        setUserData(data);
      } catch (error) {
        console.error("Error loading user data:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadUserData();
  }, []);

  // Login mutation with improved error handling
  const { mutateAsync: loginMutation, isError } = useLoginQuery(async rec => {
    if (rec) {
      try {
        setLoginModal(false);

        // Add timestamp for session expiration
        const sessionData = {
          ...rec,
          name: `${rec?.first_name} ${rec?.last_name}`,
          timestamp: Date.now(),
        };

        // Save to storage
        await StorageService.setItem(UPACE_TOKEN, sessionData);

        // Update state
        setUserData(sessionData);

        toast.success("Successfully logged in");
      } catch (error) {
        console.error("Error saving user data:", error);
        toast.error("Failed to save login information");
      }
    }
  });

  // Enhanced sign in function
  const signIn = useCallback(
    async ({
      email,
      password,
      orgId,
    }: {
      email: string;
      password: string;
      orgId?: string;
    }) => {
      setIsLoading(true);
      try {
        await loginMutation({ email, password, orgId });
      } catch (error) {
        toast.error(
          "Login failed. Please check your credentials and try again."
        );
        console.error("Login error:", error);
      } finally {
        setIsLoading(false);
      }
    },
    [loginMutation]
  );

  // Enhanced sign out function
  const handleSignOut = useCallback(async () => {
    setIsLoading(true);
    try {
      await signOut();
      setUserData(undefined);
      toast.success("Successfully logged out");
    } catch (error) {
      console.error("Error signing out:", error);
      toast.error("Failed to sign out");
    } finally {
      setIsLoading(false);
    }
  }, []);

  return (
    <CurrentUserContext.Provider
      value={{
        signIn,
        data: userData,
        isError,
        signOut: handleSignOut,
        isLoading,
      }}
    >
      {children}
    </CurrentUserContext.Provider>
  );
};
