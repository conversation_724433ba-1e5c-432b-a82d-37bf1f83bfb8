"use client";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";

import { useEffect, useState } from "react";
import Image from "next/image";

import { LoginForm, LoginSchema } from "./login-form";
import { z } from "zod";
import { useStoreValue } from "@/app/StoreContext";
import { Button } from "@/components/ui/button";
import { ResetPassword } from "./reset-password";
import { ScanQRCode } from "../scan-qr-code";
import { useSession } from "./auth-provider";
import { AppleSVG } from "./apple-svg";
import { GoogleSVG } from "./google-svg";
import { useDeviceDetect } from "@/common/hooks/useDeviceDetect";
import { useRouter } from "next/navigation";

export function LoginModal({
  appUrls,
  configs,
  orgId,
  buttonColor,
}: {
  configs: Record<string, unknown>;
  appUrls: Record<string, string>;
  orgId: string;
  buttonColor?: string;
}) {
  const [isResetPassword, setIsResetPassword] = useState(false);
  const [mounted, setMounted] = useState(false);
  const { isMobile } = useDeviceDetect();

  // Only render device-specific content after component is mounted on the client
  useEffect(() => {
    setMounted(true);
  }, []);

  const { state: shouldShowLoginModal, dispatch } = useStoreValue(
    state => state.shouldShowLoginModal
  );

  const { signIn, isError } = useSession();

  const handleSubmit = async (data: z.infer<typeof LoginSchema>) => {
    signIn({
      email: data.email,
      password: data.password,
      orgId,
    });
  };

  const redirect = useRouter();

  return (
    <Dialog
      open={shouldShowLoginModal}
      onOpenChange={() => {
        dispatch(() => ({ shouldShowLoginModal: false }));
        if (isResetPassword) {
          setIsResetPassword(false);
        }
      }}
    >
      <DialogContent className='lg:max-w-[800px] grid lg:grid-cols-2 mt-4'>
        <Image
          src={
            (configs?.class_checkin as { background_image_url: string })
              ?.background_image_url
          }
          alt='login-image'
          className='hidden lg:block h-full w-full object-cover'
          width={500}
          height={500}
        />

        <div>
          <DialogHeader>
            <DialogTitle style={{ color: buttonColor }} className='text-center'>
              {isResetPassword
                ? "Reset your password"
                : "Login to Make Reservation"}
            </DialogTitle>
          </DialogHeader>
          {isResetPassword ? (
            <ResetPassword />
          ) : (
            <LoginForm handleSubmit={handleSubmit} isError={isError} />
          )}

          <div className='flex justify-center items-center text-center'>
            {!isResetPassword && (
              <Button
                onClick={() => setIsResetPassword(prev => !prev)}
                variant='ghost'
                className='text-center items-center'
              >
                First time login / Forgot password
              </Button>
            )}
          </div>

          {!isResetPassword && (
            <div className='mt-4'>
              <div className='relative'>
                <div className='absolute inset-0 flex items-center'>
                  <span className='w-full border-t' />
                </div>
                <div className='relative flex justify-center text-xs uppercase'>
                  <span className='bg-background px-2 text-muted-foreground'>
                    Or continue with
                  </span>
                </div>
              </div>
            </div>
          )}

          <DialogDescription className='mt-4 text-center'>
            <strong className='mb-2 block'>Download Our Mobile App</strong>
            Easily manage your reservations with our mobile app
          </DialogDescription>
          {mounted &&
            (isMobile ? (
              <div className='flex gap-2 items-center justify-center'>
                <AppleSVG
                  className='cursor-pointer'
                  onClick={() => redirect.push(String(appUrls?.ios))}
                />
                <GoogleSVG
                  className='cursor-pointer'
                  onClick={() => redirect.push(String(appUrls?.android))}
                />
              </div>
            ) : (
              <ScanQRCode
                androidUrl={String(appUrls?.android)}
                iosUrl={String(appUrls?.ios)}
              />
            ))}
        </div>
      </DialogContent>
    </Dialog>
  );
}
