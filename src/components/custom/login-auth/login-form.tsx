"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useEffect } from "react";

import { IoLockClosedOutline } from "react-icons/io5";
import { FaRegEnvelope } from "react-icons/fa";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

export const LoginSchema = z.object({
  email: z.string().min(2, {
    message: "Email is required",
  }),
  password: z.string().min(1, {
    message: "Password is required",
  }),
});

export function LoginForm({
  handleSubmit,
  isError,
  isLoading,
  placeholder = "Login",
  className,
}: Readonly<{
  handleSubmit: (data: z.infer<typeof LoginSchema>) => void;
  placeholder?: string;
  isError?: boolean;
  isLoading?: boolean;
  className?: string;
}>) {
  const formControl = useForm<z.infer<typeof LoginSchema>>({
    resolver: zodResolver(LoginSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  async function onSubmit(data: z.infer<typeof LoginSchema>) {
    handleSubmit(data);
  }

  useEffect(() => {
    if (isError) {
      formControl.reset();
      formControl.setError("root", {
        message: "Your email or password is invalid",
      });
    }
  }, [formControl, isError]);

  const errors = formControl.formState.errors;

  return (
    <Form {...formControl}>
      <form
        onSubmit={formControl.handleSubmit(onSubmit)}
        className=' grid gap-4 pt-8'
      >
        {errors?.root && (
          <FormMessage className='text-center'>
            {errors?.root?.message}
          </FormMessage>
        )}
        <FormField
          control={formControl.control}
          name='email'
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className='relative col-span-3'>
                  <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                    <FaRegEnvelope className='text-gray-500' />
                  </div>
                  <Input
                    className='pl-8 text-base'
                    placeholder='Enter Email Address'
                    type='email'
                    {...field}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={formControl.control}
          name='password'
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <div className='relative col-span-3'>
                  <div className='absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none'>
                    <IoLockClosedOutline className='text-gray-500' />
                  </div>
                  <Input
                    className='pl-8 text-base'
                    placeholder='Password'
                    type='password'
                    {...field}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className='flex justify-center'>
          <Button
            disabled={isLoading}
            type='submit'
            className={cn(
              "text-white bg-blue-600 mt-4 hover:bg-blue-600 w-full lg:w-20 flex items-center",
              className
            )}
          >
            {isLoading ? (
              <Loader2 className='mr-2 h-4 w-4 animate-spin' />
            ) : (
              placeholder
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
