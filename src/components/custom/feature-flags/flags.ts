"use client";

import { useQueryState, parseAsBoolean } from "nuqs";
import { FeatureFlag } from "./feature-flags";

/**
 * Available feature flags in the application
 */
export enum FeatureFlags {
  SPOTS = "spots",
  ADVANCED_FILTERS = "advanced_filters",
  DARK_MODE = "dark_mode",
}

/**
 * Hook to get default feature flags from URL parameters
 * This allows enabling/disabling features via URL parameters
 */
export const useDefaultFlags = (): FeatureFlag[] => {
  // Get feature flags from URL parameters with defaults
  const [spots] = useQueryState(
    FeatureFlags.SPOTS,
    parseAsBoolean.withDefault(true)
  );

  const [advancedFilters] = useQueryState(
    FeatureFlags.ADVANCED_FILTERS,
    parseAsBoolean.withDefault(false)
  );

  const [darkMode] = useQueryState(
    FeatureFlags.DARK_MODE,
    parseAsBoolean.withDefault(false)
  );

  // Return feature flags array
  return [
    {
      name: FeatureFlags.SPOTS,
      isActive: <PERSON><PERSON><PERSON>(spots),
    },
    {
      name: FeatureFlags.ADVANCED_FILTERS,
      isActive: <PERSON><PERSON><PERSON>(advancedFilters),
    },
    {
      name: FeatureFlags.DARK_MODE,
      isActive: Boolean(darkMode),
    },
  ];
};
