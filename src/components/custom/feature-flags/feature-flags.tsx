import React, {
  createContext,
  useContext,
  ReactNode,
  useMemo,
  FC,
  useCallback,
} from "react";

/**
 * Feature flag interface
 */
export interface FeatureFlag {
  name: string;
  isActive: boolean;
}

/**
 * Feature flags map for faster lookups
 */
export type FeatureFlagsMap = Map<string, boolean>;

/**
 * Context type for FeatureFlags
 */
export interface FeatureFlagsContextValue {
  flags: FeatureFlag[];
  isEnabled: (flagName: string) => boolean;
}

/**
 * Default context value
 */
const defaultContextValue: FeatureFlagsContextValue = {
  flags: [],
  isEnabled: () => false,
};

/**
 * Create FeatureFlags context
 */
export const FeatureFlagsContext =
  createContext<FeatureFlagsContextValue>(defaultContextValue);

/**
 * Custom hook to use FeatureFlags context
 */
export const useFeatureFlags = (): FeatureFlagsContextValue => {
  return useContext(FeatureFlagsContext);
};

/**
 * Custom hook to check if a specific feature flag is active
 * @param flagName - The name of the feature flag to check
 * @returns A boolean indicating whether the feature flag is active
 */
export const useFeatureFlag = (flagName: string): boolean => {
  const { isEnabled } = useFeatureFlags();
  return isEnabled(flagName);
};

/**
 * Props for FlagsProvider
 */
interface FlagsProviderProps {
  value: FeatureFlag[];
  children: ReactNode;
}

/**
 * FlagsProvider component with optimized flag lookup
 */
export const FlagsProvider: FC<FlagsProviderProps> = ({ value, children }) => {
  // Create a map for O(1) lookups
  const flagsMap = useMemo(() => {
    const map = new Map<string, boolean>();
    value.forEach(flag => map.set(flag.name, flag.isActive));
    return map;
  }, [value]);

  // Optimized function to check if a flag is enabled
  const isEnabled = useCallback(
    (flagName: string): boolean => {
      return flagsMap.get(flagName) || false;
    },
    [flagsMap]
  );

  // Create context value
  const contextValue = useMemo(
    () => ({
      flags: value,
      isEnabled,
    }),
    [value, isEnabled]
  );

  return (
    <FeatureFlagsContext.Provider value={contextValue}>
      {children}
    </FeatureFlagsContext.Provider>
  );
};

/**
 * Props for Flags component
 */
interface FlagsProps {
  authorizedFlags: string[];
  exactFlags?: boolean;
  renderOn?: () => ReactNode;
  renderOff?: () => ReactNode;
  children?: ReactNode;
}

/**
 * Optimized Flags component for conditional rendering based on feature flags
 */
export const Flags: FC<FlagsProps> = ({
  authorizedFlags,
  exactFlags = false,
  renderOn = () => null,
  renderOff = () => null,
  children,
}) => {
  const { isEnabled } = useFeatureFlags();

  // Check if all required flags are enabled
  const shouldRender = useMemo(() => {
    if (exactFlags) {
      // All flags must be enabled
      return authorizedFlags.every(flag => isEnabled(flag));
    }
    // At least one flag must be enabled
    return authorizedFlags.some(flag => isEnabled(flag));
  }, [authorizedFlags, exactFlags, isEnabled]);

  // Render content based on flag status
  return <>{shouldRender ? children || renderOn() : renderOff()}</>;
};
