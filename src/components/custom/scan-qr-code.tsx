import { QRCodeSVG } from "qrcode.react";

export const ScanQRCode = ({
  iosUrl,
  androidUrl,
  // label = "Scan the QR codes with your camera",
}: {
  iosUrl: string;
  androidUrl: string;
  label?: string;
}) => {
  return (
    <div className='py-4'>
      {/* <p className='text-center'>{label}</p> */}
      <div className='flex gap-10 items-center justify-center pt-4'>
        <div>
          <QRCodeSVG
            style={{ width: 100, padding: 0, margin: 0 }}
            value={iosUrl}
            id='ios-qr-code'
          />
          <div className='flex items-center gap-1'>Apple Store</div>
        </div>
        <div>
          <QRCodeSVG
            style={{ width: 100, padding: 0, margin: 0 }}
            value={androidUrl}
            id='android-qr-code'
          />
          <div className='flex items-center pr-2'>Google Play</div>
        </div>
      </div>
    </div>
  );
};
