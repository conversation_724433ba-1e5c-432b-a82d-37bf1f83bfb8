/* eslint-disable @typescript-eslint/ban-ts-comment */
"use client";
import { ReactNode } from "react";
import { isEmpty, uniqueId } from "lodash/fp";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from "@/components/ui/table";

import { StyledTableHeader } from "@/components/data-table/data-table";

interface TableColumn<T> {
  id: string;
  label: string;
  renderCell?: (item: T, column: string) => ReactNode | string;
  isGreyed?: (item: T, column: string) => boolean;
}

interface TableProps<T> {
  columns: TableColumn<T>[];
  data: T[];
  className?: string;
  color?: string;
  renderEmptyCell?: (item: T) => React.ReactNode;
  emptyStateMessage?: string;
  isLoading?: boolean;
}

export const ScheduleTable = <T,>({
  columns = [],
  data,
  color,
  renderEmptyCell,
  emptyStateMessage,
  isLoading,
}: TableProps<T>) => {
  return (
    <div className='overflow-auto max-h-[80vh] mb-20'>
      <Table>
        <StyledTableHeader color={color}>
          <TableRow>
            {columns.map(columnName => (
              <TableHead
                className='border-r-2 border-white text-center font-black w-4'
                key={columnName?.id}
              >
                {columnName?.label}
              </TableHead>
            ))}
          </TableRow>
        </StyledTableHeader>

        <TableBody>
          {!isLoading && isEmpty(data) && (
            <TableRow>
              <TableCell
                colSpan={columns.length}
                className='text-center text-lg mt-4'
              >
                {emptyStateMessage}
              </TableCell>
            </TableRow>
          )}

          {data.map(item => {
            return (
              <TableRow key={uniqueId("sch_")}>
                <TableCell
                  className='text-center font-black sticky left-0'
                  style={{
                    backgroundColor: color,
                    color: "white",
                    position: "sticky",
                  }}
                >
                  {renderEmptyCell?.(item)}
                </TableCell>
                {columns
                  .filter(val => Boolean(val.id))
                  .map(column => {
                    const isCellGreyed = column?.isGreyed?.(item, column.id);
                    return (
                      <TableCell
                        className={`${isCellGreyed ? "bg-gray-100 border-2 border-white" : ""}`}
                        key={column.id}
                      >
                        {column.renderCell?.(item, column.id)}
                      </TableCell>
                    );
                  })}
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
};
