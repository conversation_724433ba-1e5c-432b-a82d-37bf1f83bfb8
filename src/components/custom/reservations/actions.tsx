import { ClassDetailsResponse } from "../../../app/classes/[orgId]/types";
import { CancelButton } from "../cancel-button";
import { LeaveWaitlistButton } from "../../../app/classes/[orgId]/components/leave-reservation";

export const Actions = ({ data }: { data: ClassDetailsResponse }) => {
  if (data?.position_index) {
    return (
      <LeaveWaitlistButton id={data.id} positionIndex={data.position_index} />
    );
  }

  return <CancelButton id={data.id} />;
};
