import { range } from "lodash/fp";
import { Column } from "./reservation-table";
import { TableCell, TableRow } from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";

export const ReservationSkeleton = ({ columns }: { columns: Column[] }) => {
  return range(0, columns.length).map(() => (
    <TableRow key={Math.random()}>
      {columns.map(() => (
        <TableCell key={Math.random()}>
          <Skeleton className='h-4 w-full bg-gray-300' />
        </TableCell>
      ))}
    </TableRow>
  ));
};
