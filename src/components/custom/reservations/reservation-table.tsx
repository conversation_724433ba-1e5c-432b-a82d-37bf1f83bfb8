"use client";

import { Fragment, ReactNode, useMemo } from "react";
import { groupBy, isEmpty } from "lodash/fp";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from "@/components/ui/table";

import { StyledTableHeader } from "@/components/data-table/data-table";
import { ClassDetailsResponse } from "../../../app/classes/[orgId]/types";
import { ReservationSkeleton } from "./reservation-loader";

/**
 * Column definition for reservation table
 */
export interface Column {
  name: string;
  renderCell: (item: ClassDetailsResponse) => ReactNode | string;
}

/**
 * Props for ReservationTable component
 */
interface ReservationTableProps {
  isPending: boolean;
  data: ClassDetailsResponse[];
  formatReservationDate: (date: string) => string;
  columns: Column[];
  color?: string;
  emptyMessage?: string;
}

/**
 * Optimized Reservation Table component
 */
export function ReservationTable({
  isPending,
  data,
  formatReservationDate,
  columns,
  color,
  emptyMessage = "You currently do not have any active reservations, please go back to the previous screen to make a reservation.",
}: ReservationTableProps) {
  // Group data by date for better organization
  const groupedData = useMemo(
    () =>
      groupBy(
        (rec: ClassDetailsResponse) => formatReservationDate(rec.start_time),
        data
      ),
    [data, formatReservationDate]
  );

  // Determine if there's no data to display
  const hasNoData = !isPending && isEmpty(groupedData);

  return (
    <div className='overflow-auto max-h-[500px]'>
      <Table>
        {/* Table Header */}
        <StyledTableHeader color={color}>
          <TableRow>
            {columns.map(column => (
              <TableHead key={column.name}>{column.name}</TableHead>
            ))}
          </TableRow>
        </StyledTableHeader>

        <TableBody>
          {/* Loading State */}
          {isPending && <ReservationSkeleton columns={columns} />}

          {/* Empty State */}
          {hasNoData && (
            <TableRow>
              <TableCell
                colSpan={columns.length}
                className='text-center text-lg mt-4 py-8'
              >
                {emptyMessage}
              </TableCell>
            </TableRow>
          )}

          {/* Data Display */}
          {!isPending &&
            Object.entries(groupedData).map(([day, items]) => (
              <Fragment key={`day-${day}`}>
                {/* Day Header */}
                <TableRow className='hover:bg-white'>
                  <TableCell
                    colSpan={columns.length}
                    className='font-bold mb-4 pt-4 pb-4 bg-gray-50'
                  >
                    {day?.toUpperCase()}
                  </TableCell>
                </TableRow>

                {/* Day Items */}
                {items.map(item => (
                  <TableRow key={`item-${item.id}`}>
                    {columns.map(column => (
                      <TableCell key={`cell-${item.id}-${column.name}`}>
                        {column.renderCell(item)}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </Fragment>
            ))}
        </TableBody>
      </Table>
    </div>
  );
}
