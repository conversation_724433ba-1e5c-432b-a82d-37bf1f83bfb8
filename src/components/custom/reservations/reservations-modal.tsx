"use client";
import { <PERSON><PERSON>, DialogContent, DialogHeader } from "@/components/ui/dialog";

import { useStoreValue } from "@/app/StoreContext";

import { TimeCell } from "../../../app/classes/[orgId]/modules/overview-block/columns/helper-components";
import { useUserReservations } from "../../../app/classes/[orgId]/queries/useUserReservations";
import { Actions } from "./actions";
import { ReservationTable } from "./reservation-table";
import { useMemo } from "react";
import { isMobileOnly } from "react-device-detect";
import { ClassDetailsResponse } from "../../../app/classes/[orgId]/types";
import { DetailsCell, formatReservationDate } from "./helper-components";

export function ReservationsModal() {
  const { state: showMyReservations, dispatch } = useStoreValue(
    state => state.showMyReservations
  );

  const { state: configs } = useStoreValue(state => state.configs);

  const { isPending, data } = useUserReservations(showMyReservations);

  const columns = useMemo(() => {
    if (isMobileOnly) {
      return [
        {
          name: "Details",
          renderCell: (item: ClassDetailsResponse) => (
            <DetailsCell data={item} />
          ),
        },
        {
          name: "Action",
          renderCell: (item: ClassDetailsResponse) => <Actions data={item} />,
        },
      ];
    }

    return [
      {
        name: "TIME",
        renderCell: (item: ClassDetailsResponse) => <TimeCell data={item} />,
      },
      {
        name: "NAME",
        renderCell: (item: ClassDetailsResponse) =>
          item?.class_name ?? item?.equipment_name,
      },
      {
        name: "INSTRUCTOR",
        renderCell: (item: ClassDetailsResponse) =>
          `${item?.instructor_first_name || ""} ${item?.instructor_last_name || ""}`,
      },
      {
        name: "LOCATION",
        renderCell: (item: ClassDetailsResponse) => item.gym_name,
      },
      {
        name: "ROOM",
        renderCell: (item: ClassDetailsResponse) => item.room_name,
      },
      {
        name: "",
        renderCell: (item: ClassDetailsResponse) => <Actions data={item} />,
      },
    ];
  }, []);

  return (
    <Dialog
      open={showMyReservations}
      onOpenChange={() => dispatch(() => ({ showMyReservations: false }))}
    >
      <DialogContent className='lg:max-w-5xl md:max-w-3xl'>
        <DialogHeader>
          <p className='text-center font-extrabold'>My Reservations</p>
          <ReservationTable
            isPending={isPending}
            data={data}
            formatReservationDate={formatReservationDate}
            color={configs?.accent_color}
            columns={columns}
          />
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
}
