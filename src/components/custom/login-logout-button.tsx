"use client";

import { But<PERSON> } from "@/components/ui/button";
import { LogIn, LogOut } from "lucide-react";
import { useStoreValue } from "@/app/StoreContext";
import { useSession } from "./login-auth/auth-provider";
// import { signIn } from "next-auth/react";
// import { useParams } from "next/navigation";

export const LoginLogoutButton = () => {
  const { data: sessionData, signOut } = useSession();

  const { dispatch } = useStoreValue(state => state.shouldShowLoginModal);

  // const params = useParams();

  // async function loginSalesforce() {
  //   await signIn("salesforce");
  // }

  return sessionData ? (
    <Button
      onClick={signOut}
      variant={"outline"}
      className='text-black mt-4 bg-white py-2 mx-auto md:ml-auto md:mr-0'
    >
      <LogOut className='mr-2 h-4 w-4' />
      <span>Logout</span>
    </Button>
  ) : (
    <div className='flex items-center justify-center md:justify-end space-x-4 mt-4 w-full'>
      {/* {params.orgId == "33" && (
        <Button onClick={loginSalesforce} className='text-white'>
          Login with Salesforce
        </Button>
      )} */}
      <Button
        onClick={() => dispatch(() => ({ shouldShowLoginModal: true }))}
        className='text-white'
      >
        <LogIn className='mr-2 h-4 w-4' />
        <span>Login to make reservations</span>
      </Button>
    </div>
  );
};
