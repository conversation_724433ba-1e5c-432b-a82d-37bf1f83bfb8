"use client";
import { useFormContext } from "react-hook-form";
import { FormControl, FormField, FormItem, FormMessage } from "../ui/form";
import { BaseSelect, Option } from "../ui/select";
import { cn } from "@/lib/utils";

interface FormSelectProps {
  name: string;
  placeholder: string;
  options?: Option[];
  multiple?: boolean;
  disabled?: boolean;
  className?: string;
  wrapperClassName?: string;
  label?: string;
}

export const FormSelect: React.FC<FormSelectProps> = ({
  name,
  placeholder,
  options,
  multiple,
  disabled,
  className,
  wrapperClassName,
  label,
}) => {
  const { control } = useFormContext();
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormControl>
            <BaseSelect
              {...field}
              label={label}
              onChange={value => {
                field.onChange(value);
              }}
              wrapperClassName={wrapperClassName}
              disabled={disabled}
              multiple={multiple}
              placeholder={placeholder}
              options={options}
              className={cn("lg:w-[200px] w-full", className)}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};
