import { Button } from "@/components/ui/button";
import { IoLogoGooglePlaystore } from "react-icons/io5";
import { AiOutlineApple } from "react-icons/ai";
import Link from "next/link";

export const Footer = ({
  appUrls,
  infoText,
}: {
  appUrls: Record<string, string>;
  infoText: string;
}) => {
  return (
    <div className='fixed bottom-0 w-full text-center py-4 bg-gray shadow-md flex flex-col sm:flex-row justify-center items-center gap-4  z-50 bg-white'>
      <h4 className='font-bold  text-sm sm:text-base pr-2 pl-2'>{infoText}</h4>
      <div className='flex gap-4 items-center'>
        <Button variant='outline' className='border-blue-500 md:mb-0'>
          <Link
            className='flex flex-row items-center'
            href={appUrls?.android ?? ""}
            target='__blank'
          >
            <IoLogoGooglePlaystore className='mr-2 text-center' />
            <span className='inline sm:hidden'>Google Play</span>
            <span className='hidden sm:inline'>Google Play</span>
          </Link>
        </Button>
        <Button className='border-blue-500 md:mb-0 flex' variant='outline'>
          <Link
            className='flex flex-row items-center'
            href={appUrls?.ios ?? ""}
            target='__blank'
          >
            <AiOutlineApple className='mr-2 text-center' />
            <span className='inline sm:hidden'>Apple Store</span>
            <span className='hidden sm:inline'>Apple Store</span>
          </Link>
        </Button>
      </div>
    </div>
  );
};
