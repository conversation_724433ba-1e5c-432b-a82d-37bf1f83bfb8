"use client";

import { useEffect, useRef, useState } from "react";
import {
  format,
  startOfWeek,
  addDays,
  addWeeks,
  subWeeks,
  isSameDay,
  differenceInDays,
  isAfter,
  max,
  parseISO,
} from "date-fns";

import { ChevronLeft, ChevronRight } from "lucide-react";
import { styled } from "styled-components";
import { useStoreValue } from "@/app/StoreContext";
import { Button } from "@/components/ui/button";

interface CalendarHeaderProps {
  currentMonth: Date;
}

const StyledCalendarRow = styled.div<{ color?: string }>`
  background-color: ${({ color }) => color};
  border-color: ${({ color }) => color};
`;

const CalendarHeader: React.FC<CalendarHeaderProps> = ({ currentMonth }) => {
  const dateFormat = "MMMM yyyy";
  return (
    <header className='flex py-4 text-3xl lg:text-4xl mb-2'>
      <div className='text-center items-center w-full'>
        <span>{format(currentMonth, dateFormat)}</span>
      </div>
    </header>
  );
};

interface CalendarCellProps {
  day: Date;
  isSelected: boolean;
  onDateClickHandle: (day: Date) => void;
}

const CalendarCell: React.FC<CalendarCellProps> = ({
  day,
  isSelected,
  onDateClickHandle,
}) => {
  const dateFormat = "d";
  const dayFormat = "EEE";
  const formattedDate = format(day, dateFormat);
  const formattedDay = format(day, dayFormat);
  const cloneDay = day;

  const { state: embedConfigs } = useStoreValue(state => state.configs);

  return (
    <StyledCalendarRow
      color={isSelected ? embedConfigs?.accent_color : ""}
      className={`pt-2 pb-2 text-sm justify-center text-center ${isSelected ? "text-white  border-0 font-bold py-1 rounded-sm" : ""}`}
      key={day.toString()}
      onClick={() => onDateClickHandle(cloneDay)}
      onKeyDown={event => {
        if (event.key === "Enter" || event.key === " ") {
          onDateClickHandle(cloneDay);
        }
      }}
      role='button'
      tabIndex={0}
      aria-pressed={isSelected}
    >
      <div className='mb-2'>{formattedDay}</div>
      <div>{formattedDate}</div>
    </StyledCalendarRow>
  );
};

interface CalendarRowProps {
  startDate: Date;
  endDate: Date;
  selectedDate: Date;
  onDateClickHandle: (day: Date) => void;
  changeWeekHandle: (btnType: string) => void;
  canGoPrev: boolean;
}

const CalendarRow: React.FC<CalendarRowProps> = ({
  startDate,
  endDate,
  selectedDate,
  onDateClickHandle,
  changeWeekHandle,
  canGoPrev,
}) => {
  const totalDays = differenceInDays(endDate, startDate) + 1;

  return (
    <div className='grid grid-cols-9 gap-0 lg:gap-10 w-full text-center justify-center'>
      <div className='flex items-center justify-center text-center' key='prev'>
        <button
          title='previous week'
          type='button'
          disabled={!canGoPrev}
          onClick={() => changeWeekHandle("prev")}
          className={`transition ease-in-out duration-150 ${!canGoPrev ? "opacity-50 cursor-not-allowed" : "hover:opacity-75"}`}
        >
          <ChevronLeft width={50} height={50} />
        </button>
      </div>
      {Array.from({ length: totalDays }, (_, i) => {
        const day = addDays(startDate, i);
        const isSelected = isSameDay(day, selectedDate);
        return (
          <CalendarCell
            // eslint-disable-next-line @typescript-eslint/ban-ts-comment
            //@ts-expect-error
            key={day.toString()}
            day={day}
            isSelected={isSelected}
            onDateClickHandle={onDateClickHandle}
          />
        );
      })}
      <div className='flex items-center justify-center text-center' key='next'>
        <button
          title='next week'
          type='button'
          onClick={() => changeWeekHandle("next")}
        >
          <ChevronRight width={50} height={50} />
        </button>
      </div>
    </div>
  );
};

export const WeeklyCalendar = ({
  initialDate,
  onChange,
}: {
  initialDate?: string;
  onChange?: (date: string) => void;
}) => {
  const today = useRef(parseISO(new Date().toISOString())).current;

  const [currentMonth, setCurrentMonth] = useState(today);
  const [selectedDate, setSelectedDate] = useState(today);

  const [canGoPrev, setCanGoPrev] = useState(false);

  useEffect(() => {
    setCanGoPrev(isAfter(startOfWeek(currentMonth), today));
  }, [currentMonth, today]);

  useEffect(() => {
    const initDate = initialDate ? initialDate : today.toISOString();
    const date = parseISO(initDate);
    setCurrentMonth(date);
    setSelectedDate(date);
  }, [initialDate, today]);

  const changeWeekHandle = (btnType: string) => {
    if (btnType === "prev") {
      setCurrentMonth(subWeeks(currentMonth, 1));
    }

    if (btnType === "next") {
      setCurrentMonth(addWeeks(currentMonth, 1));
    }
  };

  const onDateClickHandle = (day: Date) => {
    onChange?.(format(day, "yyyy-MM-dd"));
    setSelectedDate(day);
    setCurrentMonth(day);
  };

  const startDate = max([
    startOfWeek(currentMonth, { weekStartsOn: 0 }),
    today,
  ]);

  const endDate = addDays(startDate, 6);

  return (
    <div className='flex flex-col items-center'>
      <CalendarHeader currentMonth={currentMonth} />
      <div className='flex items-center'>
        <Button
          onClick={() => onDateClickHandle(today)}
          className='hidden lg:inline-flex'
          variant={"ghost"}
        >
          <ChevronLeft /> Back to today
        </Button>
        <CalendarRow
          startDate={startDate}
          endDate={endDate}
          selectedDate={selectedDate}
          onDateClickHandle={onDateClickHandle}
          changeWeekHandle={changeWeekHandle}
          canGoPrev={canGoPrev}
        />
      </div>
    </div>
  );
};
