"use client";

import { useStoreValue } from "@/app/StoreContext";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import { DownloadCloud, Search, XCircle } from "lucide-react";
import { useSession } from "../custom/login-auth/auth-provider";


export const TableFilters = ({
  onSearchedClasses,
  searchTerm,
  placeholder,
}: {
  onSearchedClasses: (searchTerm: string) => void;
  searchTerm: string;
  placeholder: string;
}) => {
  const { data: sessionData } = useSession();

  const { dispatch, state: checkState } = useStoreValue(
    state => state.showMyReservations
  );

  const handleDownloadPDF = () => {
    dispatch(() => ({ showDownloadPDF: true }));
  };

  return (
    <div className='flex flex-col md:flex-row gap-4 mb-3'>
      <div className='relative lg:w-[60%] w-full md:flex-grow md:mr-4'>
        <Input
          placeholder={placeholder}
          value={searchTerm}
          onChange={event => onSearchedClasses(event.target.value)}
          className='pl-10'
        />
        <Search className='absolute left-2 top-1/2 transform -translate-y-1/2 h-5 w-4 text-gray-400' />
        {searchTerm && (
          <button
            type='button'
            title='Close search'
            name='close-search'
            onClick={() => onSearchedClasses("")}
            className='absolute right-3 top-1/2 transform -translate-y-1/2'
          >
            <XCircle className='h-5 w-4 text-gray-300' />
          </button>
        )}
      </div>
      {sessionData && (
        <Button
          variant='outline'
          onClick={() => {
            dispatch(() => ({ showMyReservations: !checkState }));
          }}
        >
          View my reservations
        </Button>
      )}
      <div className='flex md:justify-start md:flex-grow lg:ml-2 '>
        <Button
          onClick={handleDownloadPDF}
          className='border-blue-500 md:mb-0 w-full'
          variant='outline'
        >
          <DownloadCloud className='mr-2 h-4 w-4 hover:bg-white' />
          <span>Download the weekly schedule</span>
        </Button>
      </div>
    </div>
  );
};
