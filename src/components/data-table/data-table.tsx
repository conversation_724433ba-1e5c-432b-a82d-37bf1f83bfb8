"use client";

import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import { Fragment, useMemo, useState } from "react";

import { TableFilters } from "@/components/data-table/table-filters";
import { Skeleton } from "../ui/skeleton";
import { range } from "lodash/fp";
import { useStoreValue } from "@/app/StoreContext";
import { styled } from "styled-components";
import { cn } from "@/lib/utils";

export const StyledTableHeader = styled(TableHeader)<{ color?: string }>`
  background: ${({ color }) => color};
`;

interface DataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  searchPlaceholder?: string;
  onRowClick?: (row: TData) => void;
  isLoading?: boolean;
  showFilters?: boolean;
  emptyState?: string | React.ReactNode;
  shouldShowHover?: boolean;
  classNames?: string;
}

const MOCK_DATA_ROWS = 7;

export function DataTable<TData, TValue>({
  columns,
  data = [],
  onRowClick,
  isLoading,
  showFilters = true,
  emptyState = "There are no classes available. Click on another date to see more classes.",
  shouldShowHover = true,
  classNames = "h-[340px]",
  searchPlaceholder = "Search by class, name, room, time or instructor",
}: Readonly<DataTableProps<TData, TValue>>) {
  const [searchTerm, setSearchTerm] = useState("");

  const mockEmptyDataRows: TData[] = useMemo(
    () => range(0, MOCK_DATA_ROWS).map(() => ({}) as TData),
    []
  );

  const table = useReactTable({
    data: isLoading ? mockEmptyDataRows : data,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onGlobalFilterChange: setSearchTerm,
    state: {
      globalFilter: searchTerm,
    },
  });

  const { state: embedConfigs } = useStoreValue(state => state.configs);

  return (
    <Fragment>
      {showFilters && (
        <TableFilters
          onSearchedClasses={setSearchTerm}
          searchTerm={searchTerm}
          placeholder={searchPlaceholder}
        />
      )}
      <div className={cn("w-full overflow-auto", classNames)}>
        <Table aria-disabled={isLoading}>
          <StyledTableHeader
            color={embedConfigs?.accent_color}
            className='z-10'
          >
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map(header => {
                  return (
                    <TableHead
                      style={{ width: header.column.columnDef?.size }}
                      className='sticky top-0 z-20'
                      key={header.id}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </StyledTableHeader>

          <TableBody>
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                  onClick={() => onRowClick?.(row.original)}
                  className={
                    shouldShowHover
                      ? "md:hover:bg-gray-100 lg:hover:bg-gray-100"
                      : ""
                  }
                >
                  {row.getVisibleCells().map(cell => {
                    return (
                      <TableCell
                        key={cell.id}
                        style={{ width: cell.column.columnDef?.size }}
                      >
                        {isLoading
                          ? flexRender(
                              () => (
                                <Skeleton className='h-4 w-full bg-gray-300 cursor-progress' />
                              ),
                              cell.getContext()
                            )
                          : flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext()
                            )}
                      </TableCell>
                    );
                  })}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className='h-24 text-center'
                >
                  {emptyState}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </Fragment>
  );
}
