"use client";

import { useCallback, useState } from "react";
import { useStoreValue } from "@/app/StoreContext";

import {
  addWeeks,
  format,
  isAfter,
  lastDayOfWeek,
  parseISO,
  startOfWeek,
  subWeeks,
} from "date-fns";
import { Option } from "../ui/select";
import { ActionMeta } from "react-select";
import { useSearchParams } from "next/navigation";
import { checkDateInCurrentWeek } from "@/app/classes/[orgId]/download/calculateWeek";

type filtersOptions = {
  initialValues?: {
    gym_id: string;
    class_category_id: string;
  };
};

export function usePDFFilters(options?: filtersOptions) {
  const { state: showDownloadPDF, dispatch } = useStoreValue(
    state => state.showDownloadPDF
  );

  const { state: configs } = useStoreValue(state => state.configs);

  const [field, setField] = useState({
    gym_id: options?.initialValues?.gym_id ?? "",
    class_category_id: options?.initialValues?.class_category_id ?? "",
  });

  const selectedDate = useSearchParams().get("date");

  const [week, setWeek] = useState(parseISO(new Date().toISOString()));
  const enableRequest = field.gym_id !== "";

  const handleChange = useCallback(
    (value: Option, actionMeta?: ActionMeta<Option>) => {
      setField(prev => ({ ...prev, [String(actionMeta?.name)]: value.value }));
    },
    []
  );

  const { startWeek, endOfWeek } = checkDateInCurrentWeek(selectedDate, week);

  const handleClose = useCallback(() => {
    setField({ gym_id: "", class_category_id: "" });
    dispatch(() => ({ showDownloadPDF: false }));
  }, [dispatch]);

  const formatStartWeek = format(startOfWeek(startWeek), "MMM d, uuuu");
  const formatEndOfWeek = format(lastDayOfWeek(endOfWeek), "MMM d, uuuu");

  const onNextWeek = useCallback(() => setWeek(addWeeks(week, 1)), [week]);

  const onPreviousWeek = useCallback(() => setWeek(subWeeks(week, 1)), [week]);

  const canGoPrev = isAfter(startWeek, new Date());

  return {
    field,
    handleChange,
    handleClose,
    startWeek,
    endOfWeek,
    enableRequest,
    showDownloadPDF,
    configs,
    onNextWeek,
    onPreviousWeek,
    canGoPrev,
    formatStartWeek,
    formatEndOfWeek,
  };
}
