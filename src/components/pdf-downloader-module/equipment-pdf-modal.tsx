"use client";

import { Option } from "../ui/select";
import { usePDFFilters } from "./usePDFFilters";

import { Dialog, DialogContent } from "../ui/dialog";
import { EquipmentPDFOverview } from "@/app/equipment/[orgId]/download/overview";

export const EquipmentDownloadPDF = ({
  categoriesOptions = [],
  facilitiesOptions = [],
  clientName,
  clientId,
  clientLogo,
  clientPdfHeader,
  appUrls,
}: {
  categoriesOptions: Option[];
  facilitiesOptions: Option[];
  clientName?: string;
  clientId?: string;
  clientLogo?: string;
  clientPdfHeader?: string;
  appUrls?: Record<string, string>;
}) => {
  const { handleClose, showDownloadPDF } = usePDFFilters();

  return (
    <Dialog open={showDownloadPDF} onOpenChange={() => handleClose()}>
      <DialogContent className='lg:w-[400px] text-center'>
        <EquipmentPDFOverview
          facilitiesOptions={facilitiesOptions}
          categoriesOptions={categoriesOptions}
          clientName={clientName}
          clientId={clientId}
          clientLogo={clientLogo}
          clientPdfHeader={clientPdfHeader}
          appUrls={appUrls}
        />
      </DialogContent>
    </Dialog>
  );
};
