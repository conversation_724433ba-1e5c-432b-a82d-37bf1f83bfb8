/* eslint-disable jsx-a11y/alt-text */

"use client";

import { Image, Text, View } from "@react-pdf/renderer";
import QRCode from "qrcode";

import { useEffect, useState } from "react";

import { Svg, Path } from "@react-pdf/renderer";

export const PDFQRCode = ({ url, label }: { url: string; label: string }) => {
  const [QRCodeString, setQRCodeString] = useState("");

  useEffect(() => {
    QRCode.toDataURL(url).then(qr => setQRCodeString(qr));
  }, [url]);

  return (
    <View style={{ display: "flex", flexDirection: "column", marginTop: 5 }}>
      <Image src={QRCodeString} style={{ width: 100 }} />
      <View style={{ display: "flex", flexDirection: "row", gap: 5 }}>
        <Text style={{ fontSize: 12, marginLeft: 20, marginTop: 5 }}>
          {label}
        </Text>
      </View>
    </View>
  );
};

export const AndroidSVG = () => (
  <Svg
    fill='#000000'
    width='20px'
    height='20px'
    viewBox='0 0 256 256'
    id='Flat'
  >
    <Path d='M207.05811,88.666q-1.10724-1.103-2.24146-2.16895l24.84009-24.84033a7.99984,7.99984,0,0,0-11.31348-11.31348L192.3728,76.3135a111.42105,111.42105,0,0,0-128.55444.19092L37.65674,50.34328A7.99984,7.99984,0,0,0,26.34326,61.65676L51.40283,86.71633A113.38256,113.38256,0,0,0,16,169.12893V192a16.01833,16.01833,0,0,0,16,16H224a16.01833,16.01833,0,0,0,16-16V168A111.25215,111.25215,0,0,0,207.05811,88.666ZM92,168a12,12,0,1,1,12-12A12,12,0,0,1,92,168Zm72,0a12,12,0,1,1,12-12A12,12,0,0,1,164,168Z' />
  </Svg>
);

export const AppleSVG = () => (
  <Svg
    fill='#000000'
    width='20px'
    height='20px'
    viewBox='0 0 256.00004 256.00004'
    id='Flat'
  >
    <Path d='M130.66016,29.63136A40.25041,40.25041,0,0,1,168,4a8,8,0,0,1,0,16,24.15408,24.15408,0,0,0-22.4082,15.38037,8.00008,8.00008,0,0,1-14.93164-5.749Zm91.38476,126.12451a40.01719,40.01719,0,0,1-2.73144-69.99951,8.00039,8.00039,0,0,0,2.14111-11.7915A68.012,68.012,0,0,0,128,61.00441,68.00487,68.00487,0,0,0,20,116c0,25.50293,8.33594,52.4668,22.87109,73.97852C57.26514,211.28224,75.63086,224,92,224h72c22.46533,0,47.92578-23.86523,61.916-58.03515A8.00438,8.00438,0,0,0,222.04492,155.75587Z' />
  </Svg>
);
