"use client";

import { Option } from "../ui/select";
import { usePDFFilters } from "./usePDFFilters";
import { Dialog, DialogContent } from "../ui/dialog";
import { ClassPDFOverview } from "@/app/classes/[orgId]/download/overview";

export const ClassDownloadPDF = ({
  categoriesOptions = [],
  facilitiesOptions = [],
  clientName,
  clientId,
  clientLogo,
  clientPdfHeader,
  appUrls,
  clientBackground,
}: {
  categoriesOptions: Option[];
  facilitiesOptions: Option[];
  clientName?: string;
  clientId?: string;
  clientLogo?: string;
  clientPdfHeader?: string;
  appUrls?: Record<string, string>;
  clientBackground?: string;
}) => {
  const { handleClose, showDownloadPDF } = usePDFFilters();

  return (
    <Dialog open={showDownloadPDF} onOpenChange={() => handleClose()}>
      <DialogContent className='lg:w-[400px] text-center'>
        <ClassPDFOverview
          facilitiesOptions={facilitiesOptions}
          categoriesOptions={categoriesOptions}
          clientName={clientName}
          clientId={clientId}
          clientLogo={clientLogo}
          clientPdfHeader={clientPdfHeader}
          appUrls={appUrls}
          clientBackground={clientBackground}
        />
      </DialogContent>
    </Dialog>
  );
};
