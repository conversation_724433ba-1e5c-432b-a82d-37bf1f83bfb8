import { obtainDateFrame } from "@/app/classes/[orgId]/classes.utils";
import { ClassDetailsResponse } from "@/app/classes/[orgId]/types";
import { StyleSheet, Text, View } from "@react-pdf/renderer";

const styles = StyleSheet.create({
  calendarView: {
    width: "108px",
    padding: 5,
  },
  calendarViewText: {
    fontSize: 11,
    paddingBottom: 5,
  },
  calendarViewName: {
    fontSize: 16,
    fontWeight: "black",
    whiteSpace: "nowrap",
    paddingBottom: 5,
  },
  calendarNameText: {
    fontSize: 11,
    paddingBottom: 5,
  },
});

export const Cell = <T extends ClassDetailsResponse>({
  row,
  column,
}: {
  row: T;
  column: string;
}) => {
  if (row.days_of_week?.includes(column)) {
    const dateInfo = row.date_info?.find(day => day.dow === column);
    return (
      <View
        style={{
          ...styles.calendarView,
          textDecoration: dateInfo?.cancelled ? "line-through" : undefined,
        }}
      >
        <Text style={styles.calendarViewText}>
          {obtainDateFrame(row.start_time, row.end_time)}
        </Text>
        <Text wrap style={styles.calendarViewName}>
          {row.name}
        </Text>
        {row.is_class_subbed ? (
          <Text
            style={styles.calendarViewText}
          >{`*${row.subbing_instructor}`}</Text>
        ) : (
          <Text style={styles.calendarNameText}>{row.instructor}</Text>
        )}
        <Text style={styles.calendarViewText}>{row.room_name}</Text>
        <View
          style={{
            height: 1,
            backgroundColor: "black",
            marginTop: 20,
          }}
        />
      </View>
    );
  }
  return null;
};
