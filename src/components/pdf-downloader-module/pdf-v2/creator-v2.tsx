/* eslint-disable jsx-a11y/alt-text */
/* eslint-disable @typescript-eslint/no-use-before-define */
/* eslint-disable @typescript-eslint/ban-ts-comment */
"use client";

import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  Font,
} from "@react-pdf/renderer";
import { ClassDetailsResponse } from "@/app/classes/[orgId]/types";
import { Footer } from "./footer";

import { Header } from "./header";
import { Cell } from "./cell";
import { useEffect } from "react";

const columns = [
  { id: "SUN", value: "SUNDAY" },
  { id: "MON", value: "MONDAY" },
  { id: "TUE", value: "TUESDAY" },
  { id: "WED", value: "WEDNESDAY" },
  { id: "THU", value: "THURSDAY" },
  { id: "FRI", value: "FRIDAY" },
  { id: "SAT", value: "SATURDAY" },
] as const;

export const ClassPDFCreatorV2 = <T extends ClassDetailsResponse>({
  data,
  clientName,
  activityType,
  weekPeriod,
  configs,
  logo,
  header,
  category,
  iosUrl,
  androidUrl,
  clientBackground,
  clientId,
}: {
  data: T[];
  clientName?: string;
  activityType?: string;
  weekPeriod?: string;
  configs?: Record<string, string>;
  logo?: string;
  header?: string;
  category?: string;
  iosUrl?: string;
  androidUrl?: string;
  clientBackground?: string;
  clientId?: string;
}) => {
  useEffect(() => {
    Font.register({
      family: configs?.fontName ?? "",
      src: configs?.fontUrl ?? "",
    });
  }, [configs?.fontName, configs?.fontUrl]);

  return (
    <Document>
      <Page wrap size='A3'>
        <View
          style={{
            margin: "2% auto",
            width: "90%",
          }}
        >
          <Header
            weekPeriod={weekPeriod}
            activityType={activityType}
            clientName={clientName}
            category={category}
            header={header}
            logo={logo}
            clientBackground={clientBackground}
            accentColor={configs?.accent_color}
          />
          <View style={styles.flexContainer}>
            {columns.map((column, colIndex) => (
              <View key={column.id}>
                {colIndex < columns.length - 1 && (
                  <View
                    style={{
                      position: "absolute",
                      right: 0,
                      top: 0,
                      bottom: 0,
                      width: 1,
                      backgroundColor: "black",
                      height: "100%",
                      zIndex: -1,
                    }}
                  />
                )}
                <View
                  style={{
                    ...styles.flexHeader,
                    backgroundColor: configs?.accent_color,
                  }}
                >
                  <Text style={styles.gridHeader}>{column.id}</Text>
                </View>

                {data.map((row, rowIndex) => (
                  <View key={`${rowIndex}-${column.id}`}>
                    <Cell row={row} column={column.id.toLowerCase()} />
                  </View>
                ))}
              </View>
            ))}
          </View>

          <View
            style={{
              height: 1,
              backgroundColor: "black",
              marginTop: 20,
            }}
          />
          <Text style={{ fontSize: 12, fontWeight: "extrabold" }}>
            *Sub Instructor
          </Text>
          <Footer
            color={configs?.accent_color}
            iosUrl={iosUrl}
            androidUrl={androidUrl}
            header={header}
            clientId={clientId}
          />
        </View>
      </Page>
    </Document>
  );
};

const styles = StyleSheet.create({
  flexContainer: {
    display: "flex",
    flexDirection: "row",
    flexWrap: "wrap",
    marginTop: 5,
  },
  flexColumn: {
    width: "108px",
    display: "flex",
    flexDirection: "column",
  },
  flexHeader: {
    width: "108px",
    alignItems: "flex-start",
    padding: 5,
    backgroundColor: "blue",
    borderRight: "1px solid black",
    color: "white",
  },
  gridHeader: {
    fontWeight: "black",
    textAlign: "left",
    fontSize: 14,
  },
  flexCell: {
    width: "108px",
    padding: 5,
    paddingTop: 0,
  },
});
