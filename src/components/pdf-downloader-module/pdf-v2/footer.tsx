"use client";
import { View, Text } from "@react-pdf/renderer";
import { PDFQRCode } from "../pdf-qrcode";

export const Footer = ({
  iosUrl,
  androidUrl,
  color,
  header,
  clientId,
}: {
  iosUrl?: string;
  androidUrl?: string;
  color?: string;
  header?: string;
  clientId?: string;
}) => {
  return (
    <View>
      <View
        style={{
          marginTop: 10,
          display: "flex",
          flexDirection: "row",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <View style={{ flex: 1, alignItems: "flex-end", borderRadius: 5 }}>
          <View
            style={{
              display: "flex",
              justifyContent: "space-between",
            }}
          >
            {/* Todo: Remove this fix when BE start sending */}
            {!["99", "65"].includes(clientId ?? "") && (
              <Text
                style={{
                  fontSize: 14,
                  fontWeight: "extrabold",
                  paddingRight: 14,
                }}
              >
                NOTES:{" "}
                <Text style={{ fontSize: 12 }}>
                  Cancellations must be made 60 minutes prior to class start
                  time. Your spot will be forfeited if you do not arrive within
                  10 minutes of the class start time. If you do not cancel and
                  do not show up for 3 classes in a month, your reservation
                  privileges will be suspended.
                </Text>
              </Text>
            )}

            <Text></Text>
          </View>
        </View>
        <View
          style={{
            display: "flex",
            flexDirection: "row",
            alignItems: "center",
            marginLeft: 15,
          }}
        >
          <PDFQRCode label={"App Store"} url={String(iosUrl)} />
          <PDFQRCode label={"Google Play"} url={String(androidUrl)} />
        </View>
      </View>
      <View style={{ marginTop: 30, backgroundColor: color }}>
        {header && (
          <Text
            style={{ fontSize: 12, padding: 10, color: "white", opacity: 0.8 }}
          >
            <Text
              style={{
                fontSize: 14,
                fontWeight: "extrabold",
                paddingRight: 14,
              }}
            >
              PLEASE NOTE: <Text style={{ fontSize: 12 }}>{header}</Text>
            </Text>
          </Text>
        )}
      </View>
    </View>
  );
};
