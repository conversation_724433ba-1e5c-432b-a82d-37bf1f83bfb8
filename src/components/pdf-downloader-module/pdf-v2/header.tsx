import { Image, Text, View, StyleSheet } from "@react-pdf/renderer";

/**
 * PDF Header component props
 */
interface HeaderProps {
  logo?: string;
  header?: string;
  category?: string;
  clientName?: string;
  activityType?: string;
  weekPeriod?: string;
  clientBackground?: string;
  accentColor?: string;
}

/**
 * Create styles for the header component
 */
const styles = StyleSheet.create({
  headerContainer: {
    position: "relative",
    height: 150,
    marginTop: "2%",
    marginBottom: "1%",
    overflow: "hidden",
    borderRadius: 4,
  },
  backgroundImage: {
    width: "100%",
    height: "100%",
    objectFit: "cover",
    position: "absolute",
    zIndex: 0,
    top: 0,
    opacity: 0.4,
  },
  contentContainer: {
    display: "flex",
    flexDirection: "row",
    marginTop: 25,
    marginLeft: 25,
    padding: 10,
    justifyContent: "space-between",
    position: "relative",
    zIndex: 1,
    width: "100%",
  },
  textContainer: {
    position: "relative",
    zIndex: 2,
    maxWidth: "70%",
  },
  title: {
    fontSize: 25,
    fontWeight: "bold",
    color: "white",
  },
  subtitle: {
    fontSize: 12,
    marginTop: 5,
    color: "white",
  },
  date: {
    fontSize: 12,
    marginTop: 20,
    color: "white",
  },
  logoContainer: {
    position: "absolute",
    right: 30,
    top: 25,
    zIndex: 2,
  },
  logo: {
    width: 60,
    height: 60,
    objectFit: "contain",
  },
});

/**
 * Optimized PDF Header component
 */
export const Header = ({
  logo,
  clientName,
  activityType,
  weekPeriod,
  clientBackground,
  accentColor = "#002966",
}: HeaderProps) => {
  // Determine title text with fallback
  const titleText = clientName
    ? `${clientName} Class Schedule`
    : "Class Schedule";

  return (
    <View
      wrap={false}
      style={[
        styles.headerContainer,
        { backgroundColor: clientBackground ? undefined : accentColor },
      ]}
    >
      {/* Background image with proper handling */}
      {clientBackground && (
        <Image
          style={styles.backgroundImage}
          src={clientBackground}
          cache={true}
        />
      )}

      {/* Content container */}
      <View style={styles.contentContainer}>
        {/* Text content */}
        <View style={styles.textContainer}>
          <Text style={styles.title}>{titleText}</Text>
          {activityType && <Text style={styles.subtitle}>{activityType}</Text>}
          {weekPeriod && (
            <Text style={styles.date}>{`As of ${weekPeriod}`}</Text>
          )}
        </View>

        {/* Logo with proper positioning */}
        {logo && (
          <View style={styles.logoContainer}>
            <Image src={logo} style={styles.logo} cache={true} />
          </View>
        )}
      </View>
    </View>
  );
};
