"use client";

import { Dialog, DialogContent, DialogHeader } from "@/components/ui/dialog";

import { BaseSelect, Option } from "../ui/select";
import { X } from "lucide-react";

import { Button } from "../ui/button";

import { PropsWithChildren } from "react";

import { ActionMeta } from "react-select";

export function PDFDownloadFilters({
  categoriesOptions = [],
  facilitiesOptions = [],
  showDownloadPDF,
  handleClose,
  handleChange,
  startWeek,
  endOfWeek,
  field,
  isDataEmpty,
  isLoading,
  categoryPlaceholder = "Select Categories",
  children,
}: PropsWithChildren<{
  categoriesOptions: Option[];
  facilitiesOptions: Option[];
  showDownloadPDF?: boolean;
  handleClose: () => void;
  handleChange: (value: Option, actionMeta?: ActionMeta<Option>) => void;
  startWeek: string;
  endOfWeek: string;
  field: { gym_id: string; class_category_id: string };
  isLoading?: boolean;
  isDataEmpty?: boolean;
  categoryPlaceholder?: string;
}>) {
  return (
    <Dialog open={showDownloadPDF} onOpenChange={() => handleClose()}>
      <DialogContent className='lg:w-[400px] text-center'>
        <DialogHeader>
          <p className='text-center'>Download weekly schedule</p>
          <p className='text-center pt-4 font-bold'>
            {startWeek} - {endOfWeek}
          </p>

          {!isLoading && isDataEmpty ? (
            <div className='text-red-500 text-center'>
              <p>There is no data to download</p>
              <p> Please select a different location</p>
            </div>
          ) : (
            <p className='text-center'>Please select location to download</p>
          )}
        </DialogHeader>
        <div className='my-5 flex flex-col gap-4'>
          <BaseSelect
            className='w-full'
            onChange={handleChange}
            placeholder='Select Location'
            options={facilitiesOptions}
            value={field?.gym_id}
            name='gym_id'
          />
          <BaseSelect
            className='w-full'
            onChange={handleChange}
            placeholder={categoryPlaceholder}
            options={categoriesOptions}
            value={field?.class_category_id}
            name='class_category_id'
          />
        </div>
        <div className='flex justify-between'>
          <Button onClick={() => handleClose()} variant={"outline"}>
            <X className='mr-2 h-4 w-4 hover:bg-white' color='blue' />
            <span>Cancel</span>
          </Button>
          {children}
        </div>
      </DialogContent>
    </Dialog>
  );
}
