/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useStoreValue } from "@/app/StoreContext";
import { cn } from "@/lib/utils";
import { HTMLAttributes, ReactElement } from "react";
import { isMobileOnly } from "react-device-detect";
import Select, { ActionMeta, GroupBase, components } from "react-select";
import ClientOnly from "./client-only-wrapper";

export type Option = {
  [key: string | number]: string | number | boolean | undefined;
  value: string | number | undefined;
  label: string | undefined;
  id?: string;
  isDisabled?: boolean;
};

export declare type OnChangeValue<
  OptionType extends Option,
  MultiType extends boolean,
> = MultiType extends true ? OptionType[] : OptionType;

export interface ComboBoxBaseProps<
  OptionType extends Option,
  MultiType extends boolean,
> {
  kind?:
    | "creatable"
    | "async"
    | "async-creatable"
    | "input"
    | "multi-creatable";
  /** The label of the combo box */
  label?: string;
  /** The value of the select. Reflected by the selected option. */
  value: OptionType | string | string[];
  /** The prop for indicatorType which is required to enable the indicator and shows the status based on its given type without label. */

  options?: Array<OptionType>;

  onClear?: () => void;
  onChange: (
    value: OnChangeValue<OptionType, MultiType> | OptionType,
    action?: ActionMeta<OptionType>
  ) => void;

  onCreateOption?: (inputValue: string) => void;

  isLoading?: boolean;
  invalid?: boolean;
  disabled?: boolean;
  id?: string;
  name?: string;
  /** This text is shown when no item is selected */
  placeholder?: string;
  /** Provide at least one label(visual or non-visual) property for better accessibility. Check Accessibility page for more info: http://atlas.adjust.com/docs/components/accessibility */
  "aria-label"?: HTMLAttributes<HTMLInputElement>["aria-label"];
  /** Provide at least one label(visual or non-visual) property for better accessibility. Check Accessibility page for more info: http://atlas.adjust.com/docs/components/accessibility */
  "aria-labelledby"?: HTMLAttributes<HTMLInputElement>["aria-labelledby"];
  /** Controls if the comboBox is open or closed */
  isOpen?: boolean;
  /** Loads the comboBox open*/
  isOpenDefault?: boolean;
  /** enables search functionality */
  isSearchable?: boolean;
  className?: string;
  wrapperClassName?: string;
  menuPlacement?: "top" | "bottom" | "auto";
}

const MoreSelectedBadge = ({ items }: any) => {
  const style = {
    marginLeft: "1px",
    background: "#d4eefa",
    borderRadius: "4px",
    fontSize: "11px",
    padding: "3px",
    order: 99,
  };

  const title = items.join(", ");
  const length = items.length;
  const label = `+ ${length}`;

  return (
    <div style={style} title={title} className='text-wrap'>
      {label}
    </div>
  );
};

const MultiValue = ({ index, getValue, ...props }: any) => {
  const maxToShow = 1;
  const overflow = getValue()
    .slice(maxToShow)
    .map((x: any) => x.label);

  return index < maxToShow ? (
    <components.MultiValue {...props} />
  ) : index === maxToShow ? (
    <MoreSelectedBadge items={overflow} />
  ) : null;
};

export interface ComboBoxCommonProps<
  OptionType extends Option,
  MultiType extends boolean,
> extends ComboBoxBaseProps<OptionType, MultiType> {
  /** The kind of the combo box */
  kind?: "creatable" | "async" | "async-creatable" | "multi-creatable";

  /** Function that returns text to display when there are no options */
  noOptionsMessage?: (data: { inputValue: string }) => string | null;

  /** Use this prop to allow multiple selection */
  multiple?: MultiType;
  closeMenuOnSelect?: boolean;
}

export declare type ComboBoxProps<
  OptionType extends Option = Option,
  MultiType extends boolean = false,
> = ComboBoxCommonProps<OptionType, MultiType>;

export const BaseSelect = <
  OptionType extends Option,
  MultiType extends boolean = false,
>(
  props: ComboBoxProps<OptionType, MultiType>
): ReactElement => {
  const {
    label,
    name,
    onChange,
    value,
    placeholder,
    options,
    className,
    multiple,
    disabled,
    wrapperClassName,
    menuPlacement,
    ...restProps
  } = props;

  // Use a stable instanceId to prevent hydration mismatches
  const instanceId = name || "select";

  const selectedOption = Array.isArray(value)
    ? options?.filter(opt => value.includes(String(opt?.value)))
    : options?.find(opt => opt?.value === value);

  const { state: embedConfigs } = useStoreValue(state => state.configs);

  return (
    <div className={cn("flex", wrapperClassName)}>
      <>
        {label && (
          <label htmlFor={name} className='font-bold'>
            {label}
          </label>
        )}
      </>
      <ClientOnly>
        <Select
          {...restProps}
          aria-label={label}
          aria-labelledby={label}
          inputId={name}
          instanceId={instanceId}
          isDisabled={disabled}
          isMulti={multiple}
          name={name}
          className={className}
          hideSelectedOptions={false}
          closeMenuOnSelect={multiple ? false : true}
          styles={{
            dropdownIndicator: base => ({
              ...base,
              color: embedConfigs?.accent_color,
            }),
            control: (base, { isFocused }) => ({
              ...base,
              borderColor: isFocused
                ? embedConfigs?.accent_color
                : embedConfigs?.accent_color,
              boxShadow: "none",
            }),
            option: (base, { isSelected }) => ({
              ...base,
              backgroundColor: isSelected
                ? embedConfigs?.accent_color
                : undefined,
              "&:hover": {
                backgroundColor: embedConfigs?.accent_color,
                color: "white",
              },
            }),
            menu: base => ({
              ...base,
              zIndex: 9999,
            }),
            multiValueLabel: base => {
              return {
                ...base,
                maxWidth: isMobileOnly ? 15 : 50,
              };
            },
          }}
          value={selectedOption}
          components={{
            IndicatorSeparator: null,
            MultiValue,
          }}
          onChange={(newValue, actionMeta) =>
            onChange?.(newValue as OptionType, actionMeta)
          }
          placeholder={placeholder}
          options={options as (OptionType | GroupBase<OptionType>)[]}
          menuPlacement={menuPlacement ?? "auto"}
        />
      </ClientOnly>
    </div>
  );
};
