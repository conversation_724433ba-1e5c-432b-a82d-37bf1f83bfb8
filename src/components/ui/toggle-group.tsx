"use client";

import * as React from "react";
import * as ToggleGroupPrimitive from "@radix-ui/react-toggle-group";
import { VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";
import { toggleVariants } from "@/components/ui/toggle";
import { useStoreValue } from "@/app/StoreContext";
import { styled } from "styled-components";

const ToggleGroupContext = React.createContext<
  VariantProps<typeof toggleVariants>
>({
  size: "default",
  variant: "default",
});

const ToggleGroup = React.forwardRef<
  React.ElementRef<typeof ToggleGroupPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ToggleGroupPrimitive.Root> &
    VariantProps<typeof toggleVariants>
>(({ className, variant, size, children, ...props }, ref) => (
  <ToggleGroupPrimitive.Root
    ref={ref}
    className={cn("flex  gap-1", className)}
    {...props}
  >
    <ToggleGroupContext.Provider value={{ variant, size }}>
      {children}
    </ToggleGroupContext.Provider>
  </ToggleGroupPrimitive.Root>
));

ToggleGroup.displayName = ToggleGroupPrimitive.Root.displayName;

const ToggleGroupItem = React.forwardRef<
  React.ElementRef<typeof ToggleGroupPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof ToggleGroupPrimitive.Item> &
    VariantProps<typeof toggleVariants>
>(({ className, children, variant, size, ...props }, ref) => {
  const context = React.useContext(ToggleGroupContext);

  return (
    <ToggleGroupPrimitive.Item
      ref={ref}
      className={cn(
        toggleVariants({
          variant: context.variant || variant,
          size: context.size || size,
        }),
        className
      )}
      {...props}
    >
      {children}
    </ToggleGroupPrimitive.Item>
  );
});

ToggleGroupItem.displayName = ToggleGroupPrimitive.Item.displayName;

export { ToggleGroup, ToggleGroupItem };

const StyledToggle = styled(ToggleGroupItem)<{ color?: string }>`
  &[data-state="on"] {
    background-color: ${({ color }) => color};
    color: white;
  }
`;

export interface ToggleProps {
  lists: { value: string; label: string }[];
  onChange?: (val: string | string[]) => void;
  type?: "single" | "multiple";
  className?: string;
}

export const Toggles = ({
  lists,
  type = "single",
  className,
  onChange,
}: ToggleProps) => {
  const { state: embedConfigs } = useStoreValue(state => state.configs);
  return (
    <ToggleGroup variant='outline' type={type} onValueChange={onChange}>
      <div className={className}>
        {lists.map(list => (
          <StyledToggle
            color={embedConfigs?.button_color}
            key={list.value}
            value={list.value}
            aria-label={list.label}
            className={cn(list.label.length > 15 ? "pt-6 pb-6" : "", "mr-4")}
          >
            <p>{list.label}</p>
          </StyledToggle>
        ))}
      </div>
    </ToggleGroup>
  );
};
