"use client";

/**
 * A service for handling browser storage operations with type safety
 */
export class StorageService {
  /**
   * Get an item from storage
   * @param key - The key to retrieve
   * @param storage - The storage to use (defaults to localStorage)
   * @returns The parsed value or undefined if not found
   */
  public static async getItem<T>(
    key: string,
    storage: Storage = localStorage
  ): Promise<T | undefined> {
    try {
      const item = storage.getItem(key);
      if (!item) {
        return undefined;
      }
      return JSON.parse(item) as T;
    } catch (error) {
      console.error(`Error retrieving item from storage: ${key}`, error);
      return undefined;
    }
  }

  /**
   * Set an item in storage
   * @param key - The key to set
   * @param value - The value to store
   * @param storage - The storage to use (defaults to localStorage)
   */
  public static async setItem<T>(
    key: string,
    value: T,
    storage: Storage = localStorage
  ): Promise<void> {
    try {
      storage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(`Error setting item in storage: ${key}`, error);
    }
  }

  /**
   * Remove an item from storage
   * @param key - The key to remove
   * @param storage - The storage to use (defaults to localStorage)
   */
  public static async removeItem(
    key: string,
    storage: Storage = localStorage
  ): Promise<void> {
    try {
      storage.removeItem(key);
    } catch (error) {
      console.error(`Error removing item from storage: ${key}`, error);
    }
  }

  /**
   * Check if storage is available
   * @returns True if storage is available, false otherwise
   */
  public static isStorageAvailable(): boolean {
    return typeof window !== "undefined";
  }
}
