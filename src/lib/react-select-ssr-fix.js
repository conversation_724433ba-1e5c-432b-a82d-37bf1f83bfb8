// This file provides a fix for React Select SSR hydration issues
import ReactDOM from "react-dom";
if (typeof window !== "undefined") {
  // Fix for React Select hydration issues
  // This ensures that the same IDs are generated on both server and client
  const originalCreatePortal = ReactDOM.createPortal;
  ReactDOM.createPortal = (children, container, ...args) => {
    if (container && container.nodeType === 1) {
      // Ensure the container has a stable ID
      if (!container.id) {
        container.id = `react-select-portal-${Math.random().toString(36).substr(2, 9)}`;
      }
    }
    return originalCreatePortal(children, container, ...args);
  };
}
