import { BASE_API_URL_CLIENT } from "@/app/classes/[orgId]/actions/constant";
import { getSession } from "@/components/custom/login-auth/auth-provider";
import ky from "ky";
import { toast } from "sonner";

/**
 * Default timeout for API requests in milliseconds
 */
const DEFAULT_TIMEOUT = 30000; // 30 seconds

/**
 * Default number of retry attempts for failed requests
 */
const DEFAULT_RETRY_COUNT = 2;

/**
 * Error handler for API requests
 * @param error - The error object from ky
 */
const handleApiError = async (error: unknown) => {
  if (error instanceof Error) {
    // Log the error for debugging
    console.error("API Error:", error);

    // Show a user-friendly error message
    toast.error("An error occurred while fetching data. Please try again.");

    // Re-throw the error for the caller to handle if needed
    throw error;
  }
};

/**
 * Enhanced API client with better error handling, caching, and retry logic
 */
export const api = ky.create({
  prefixUrl: BASE_API_URL_CLIENT,
  timeout: DEFAULT_TIMEOUT,
  retry: {
    limit: DEFAULT_RETRY_COUNT,
    methods: ["get", "post", "put", "delete", "patch"],
    statusCodes: [408, 413, 429, 500, 502, 503, 504],
  },
  cache: "default", // Use browser's default caching strategy
  headers: {
    Accept: "application/json",
    "Content-Type": "application/json",
  },
  hooks: {
    // Add authorization header before each request
    beforeRequest: [
      async request => {
        const session = await getSession();
        request.headers.set(
          "Authorization",
          session?.token ? `Bearer ${session?.token}` : ""
        );
      },
    ],
    // Handle errors after each request
    afterResponse: [
      async (request, _options, response) => {
        // If the response is not ok, handle the error
        if (!response.ok) {
          // Define a type for the error data with optional message
          interface ErrorData {
            message?: string;
            [key: string]: unknown;
          }

          // Parse the error response with proper typing
          const errorData: ErrorData = (await response
            .json()
            .catch(() => ({ message: undefined }))) as ErrorData;

          const error = new Error(
            errorData?.message ||
              `Request failed with status ${response.status}`
          );

          // Add additional context to the error
          Object.assign(error, {
            request,
            response,
            data: errorData,
          });

          handleApiError(error);
          throw error;
        }
        return response;
      },
    ],
  },
});
