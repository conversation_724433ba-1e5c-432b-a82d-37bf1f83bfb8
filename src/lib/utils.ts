import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Combines class names using clsx and tailwind-merge
 * @param inputs - Class values to merge
 * @returns Merged class string
 */
export function cn(...inputs: ClassValue[]): string {
  return twMerge(clsx(inputs));
}

/**
 * Extracts an ID from a URL path
 * @param url - URL to extract ID from
 * @returns Extracted ID or empty string if not found
 */
export function extractIdFromUrl(url: string | null): string {
  if (!url) {
    return "";
  }

  try {
    const match = url.match(/\/(\d+)(\/|\?|$)/);
    return match ? match[1] : "";
  } catch (error) {
    console.error("Error extracting ID from URL:", error);
    return "";
  }
}

/**
 * Extracts font details from a font style string
 * @param fontStyle - Font style string in format "family::weight"
 * @returns Array containing [family, weight] or empty strings if not provided
 */
export function extractFontDetails(fontStyle?: string): [string, string] {
  if (!fontStyle) {
    return ["", ""];
  }

  try {
    const parts = fontStyle.split("::");
    return parts.length === 2 ? [parts[0], parts[1]] : ["", ""];
  } catch (error) {
    console.error("Error extracting font details:", error);
    return ["", ""];
  }
}

/**
 * Updates the last segment of a URL path
 * @param url - URL to update
 * @param newValue - New value for the last segment
 * @returns Updated URL
 */
export function updateUrlLastSegment(url: string, newValue: string): string {
  if (!url) {
    return "";
  }

  try {
    // Use a safer regex that doesn't need the no-useless-escape exception
    return url.replace(/\/([^/]+)$/, `/${newValue}`);
  } catch (error) {
    console.error("Error updating URL last segment:", error);
    return url;
  }
}

/**
 * Safely parses JSON with error handling
 * @param jsonString - JSON string to parse
 * @param fallback - Fallback value if parsing fails
 * @returns Parsed object or fallback
 */
export function safeJsonParse<T>(jsonString: string, fallback: T): T {
  try {
    return JSON.parse(jsonString) as T;
  } catch (error) {
    console.error("Error parsing JSON:", error);
    return fallback;
  }
}

/**
 * Formats a date string to a localized format
 * @param dateString - ISO date string
 * @param locale - Locale for formatting (defaults to 'en-US')
 * @returns Formatted date string
 */
export function formatDate(
  dateString: string,
  locale: string = "en-US"
): string {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString(locale, {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  } catch (error) {
    console.error("Error formatting date:", error);
    return dateString;
  }
}
