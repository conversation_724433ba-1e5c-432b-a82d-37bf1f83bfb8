/* eslint-disable @typescript-eslint/ban-ts-comment */
import { NextResponse, type NextRequest } from "next/server";

export async function middleware(request: NextRequest) {
  if (request.url.includes("auth/login/legacy") && request.method === "POST") {
    return NextResponse.next();
  }
  return NextResponse.next({
    request: {
      headers: new Headers({ "x-url": request.url }),
    },
  });
}

export const config = {
  matcher: [
    "/classes/:path*",
    "/equipment/:path*",
    "/personal-training/:path*",
    "/challenge/:path*",
    "/list/:path*",
    // "/test/:path*",
  ],
};
