"use client";

import { getSession } from "@/components/custom/login-auth/auth-provider";
import { BASE_API_URL_CLIENT } from "../classes/[orgId]/actions/constant";

export const reserveAction = async (data: unknown) => {
  const session = await getSession();

  const headers: { "Content-Type": string; authorization?: string } = {
    "Content-Type": "application/json",
  };

  if (session) {
    headers.authorization = `Bearer ${session?.token}`;
  }

  try {
    const response = await fetch(`${BASE_API_URL_CLIENT}/reserve`, {
      headers,
      method: "POST",
      body: JSON.stringify(data),
    });

    const result = await response.json();

    if (result?.success) {
      return await result;
    }
    throw new Error(`${result.message}`);
  } catch (err) {
    throw new Error(`${err}`);
  }
};
