"use server";

import { BASE_API_URL } from "@/common/api/constants";
import { ChallengeType } from "../types";

export const fetchChallenges = async (
  orgId: string,
  params?: Record<string, string | number>
): Promise<ChallengeType[]> => {
  try {
    const urlParams = new URLSearchParams({
      ...params,
      university_id: orgId,
    }).toString();
    const rec = await fetch(
      `${BASE_API_URL}/challenges/list/unauth?${urlParams}&active_only=true`,
      {
        cache: "no-cache",
        next: { revalidate: 1800 }, // 30 minutes
      }
    );
    const response = await rec.json();
    return await response.data;
  } catch (err) {
    throw new Error("Could not fetch configs");
  }
};
