import { Footer } from "@/components/custom/footer";
import { ChallengeOverview } from "./overview/overview";
import { fetchClientConfigs } from "@/common/api/fetchClientConfigs";
import { fetchChallenges } from "./actions/fetchChallenges";

export default async function ChallengePage({
  params,
}: Readonly<{
  params: {
    orgId: string;
  };
}>) {
  const data = await fetchClientConfigs(params?.orgId);
  const challenges = await fetchChallenges(params?.orgId);

  return (
    <div className='mb-24 mt-4'>
      <ChallengeOverview challenges={challenges} />
      <Footer
        appUrls={data?.appUrls}
        infoText='Download the mobile app to participate'
      />
    </div>
  );
}
