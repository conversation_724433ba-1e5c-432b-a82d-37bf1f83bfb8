import { Button } from "@/components/ui/button";
import Link from "next/link";

export const ChallengeDescription = ({
  description,
  goals,
  url,
}: {
  description: string;
  goals?: string[];
  url?: string;
}) => (
  <div className='lg:pl-10'>
    <p>{description}</p>
    {goals?.length && (
      <ul className='list-disc pl-5 mt-4'>
        {goals.map(goal => (
          <li className='pt-2' key={goal}>
            {goal}
          </li>
        ))}
      </ul>
    )}

    {url && (
      <Button variant={"ghost"} className='mt-4'>
        <Link href={url} target='_blank' className='underline'>
          Watch the video
        </Link>
        <span className='ml-1'> for tips on planks</span>
      </Button>
    )}
  </div>
);
