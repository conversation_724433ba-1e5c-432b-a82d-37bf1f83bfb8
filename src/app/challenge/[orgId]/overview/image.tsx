"use client";
import { useStoreValue } from "@/app/StoreContext";
import Image from "next/image";

export const ChallengeImage = ({
  imageSrc,
  text,
}: {
  imageSrc?: string;
  text?: string;
}) => {
  const { state: configs } = useStoreValue(state => state.configs);

  const color = configs?.accent_color?.replace("#", "");

  if (!imageSrc) {
    const imageLoader = `https://placehold.co/280x280/${color}/white?text=${text}`;
    return (
      <Image
        className='rounded-full w-82 h-82'
        src={imageLoader}
        height={300}
        width={300}
        alt='upace-image'
        loader={({ src }) => src}
        // layout='responsive'
      />
    );
  }

  return (
    <Image
      className='rounded object-center w-full lg:w-5/6 h-72'
      src={imageSrc}
      height={500}
      width={500}
      alt='upace-image'
      // layout='responsive'
    />
  );
};
