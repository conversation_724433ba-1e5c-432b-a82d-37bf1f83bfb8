"use client";
import { useStoreValue } from "@/app/StoreContext";
import { styled } from "styled-components";
import tw from "twin.macro";
import { ChallengeDetails } from "./details";
import { ChallengeType } from "../types";
import { ChallengeDescription } from "./description";
import { format } from "date-fns";
import { ChallengeImage } from "./image";

const formatDate = (date: string) => format(new Date(date), "do MMM yyyy");

const Header = styled.h1<{ color?: string }>`
  ${tw`mb-4 font-extrabold text-3xl`};
  color: ${({ color }) => color};
`;

export const ChallengeOverview = ({
  challenges,
}: {
  challenges: ChallengeType[];
}) => {
  const { state: configs } = useStoreValue(state => state.configs);

  if (!challenges.length) {
    return (
      <div className='flex items-center justify-center'>
        <h1>No challenges available</h1>
      </div>
    );
  }

  return (
    <div className='mb-16'>
      {challenges.map((challenge, index) => (
        <div key={index} className='container mb-8'>
          <Header color={configs?.accent_color}>{challenge.title}</Header>
          <div className='grid lg:grid-cols-2 grid-cols-1 gap-6 pb-6'>
            <div className='lg:border-r pr-10'>
              <ChallengeImage
                imageSrc={challenge.default_image}
                text={challenge.title}
              />
              <ChallengeDetails
                daysLeft={challenge.days_left}
                participants={challenge.challenge_participations_count}
                date={`${formatDate(challenge.start_date)} - ${formatDate(challenge.end_date)}`}
                challengeReward={challenge.reward}
              />
            </div>
            <ChallengeDescription
              description={challenge.description}
              // goals={challenge.goals}
              // url={challenge.videoUrl}
            />
          </div>
          {index < challenges.length - 1 && <hr />}
        </div>
      ))}
    </div>
  );
};
