import { InfoDetailsRow } from "@/components/custom/info-details-rows";
import { CalendarDays, Hourglass, TrophyIcon, Users } from "lucide-react";

export const ChallengeDetails = ({
  date,
  daysLeft,
  participants,
  challengeReward = "Be entered to win a personal training session",
}: {
  date: string;
  daysLeft: number;
  participants: number;
  challengeReward?: string;
}) => (
  <div className='mt-4'>
    <InfoDetailsRow icon={<CalendarDays />}>{date}</InfoDetailsRow>
    <InfoDetailsRow icon={<Hourglass className='w-6 h-6' />}>
      {`${daysLeft} Days Left`}
    </InfoDetailsRow>
    <InfoDetailsRow icon={<Users className='w-6 h-6' />}>
      {`${participants} Participants`}
    </InfoDetailsRow>
    <InfoDetailsRow icon={<TrophyIcon className='w-6 h-6' />}>
      {challengeReward}
    </InfoDetailsRow>
  </div>
);
