export interface ChallengeType {
  id: number;
  created: string;
  updated: string;
  title: string;
  description: string;
  start_date: string;
  end_date: string;
  challenge_type: string;
  challenge_rules: string;
  wearable_connection_required: number;
  reward_type: string;
  reward: string;
  external_document: null | string;
  default_image: string;
  university_id: number;
  active: number;
  deleted: number;
  days_left: number;
  challenge_participations_count: number;
  has_challenge_participation: null | boolean;
}
