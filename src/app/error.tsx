"use client";

import { But<PERSON> } from "@/components/ui/button";

export default function Error({
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  return (
    <div className='flex flex-col items-center justify-center min-h-screen p-4 text-center'>
      <h2 className='text-2xl font-bold mb-4'>Something went wrong</h2>
      <p className='mb-6 text-gray-600'>
        We apologize for the inconvenience. Please try again or contact support
        if the problem persists.
      </p>
      <Button onClick={reset} className='px-4 py-2 rounded'>
        Try again
      </Button>
    </div>
  );
}
