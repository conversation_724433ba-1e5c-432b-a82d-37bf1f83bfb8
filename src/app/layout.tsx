/* eslint-disable import/no-unassigned-import */
import type { Metadata } from "next";
import "./globals.css";
import Provider from "./providers";
import GlobalStyles from "@/styles/GlobalStyles";
import { Toaster } from "sonner";
import { extractFontDetails, extractIdFromUrl } from "@/lib/utils";
import { headers } from "next/headers";
import { fetchClientConfigs } from "@/common/api/fetchClientConfigs";

import { NuqsAdapter } from "nuqs/adapters/next/app";

export const metadata: Metadata = {
  title: "upace app",
  description: "upace | Fitness Meet Convenience",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const _headers = headers();

  const url = _headers.get("x-url");
  const params = extractIdFromUrl(url);

  // Default values in case we can't fetch client configs
  let embedsConfigs = {};
  let sgtPurchaseUrl = "";

  try {
    // Only fetch client configs if we have a valid organization ID
    if (params) {
      const clientConfigs = await fetchClientConfigs(String(params));
      embedsConfigs = clientConfigs.embedsConfigs || {};
      sgtPurchaseUrl = clientConfigs.sgtPurchaseUrl || "";
    }
  } catch (error) {
    console.error("Error fetching client configs:", error);
    // Continue with default values
  }

  const [fontName, fontUrl] = extractFontDetails(
    (embedsConfigs as { font_style?: string })?.font_style
  );

  return (
    <html lang='en'>
      <head>
        {fontUrl && <link href={fontUrl} rel='stylesheet' />}
        <link
          rel='icon'
          href='https://upaceapp.com/img/favicon.png'
          type='image/png'
        />
        {/* Script to help with hydration issues */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              (function() {
                // Define self if it doesn't exist (for SSR)
                if (typeof self === 'undefined') {
                  window.self = window;
                }
              })();
            `,
          }}
        />
      </head>

      <body
        style={{
          /* Use inline style for the font-family */
          fontFamily: fontName || "system-ui, sans-serif",
        }}
      >
        <Toaster richColors position='top-center' />
        <GlobalStyles />
        <NuqsAdapter>
          <Provider
            initialState={{
              ...embedsConfigs,
              fontName: fontName || "",
              fontUrl: fontUrl || "",
              sgtPurchaseUrl,
            }}
          >
            {children}
          </Provider>
        </NuqsAdapter>
      </body>
    </html>
  );
}
