import { fetchClientConfigs } from "@/common/api/fetchClientConfigs";
import { RequestTrainerForm } from "./request-trainer-form";

export default async function RequestTrainer({
  params,
}: {
  params: {
    orgId: string;
  };
}) {
  const { facilitiesOptions, ptCategoriesOptions, ptSessionsOptions } =
    await fetchClientConfigs(params.orgId);
  return (
    <div className='flex container'>
      <RequestTrainerForm
        facilitiesOptions={facilitiesOptions}
        ptCategoriesOptions={ptCategoriesOptions}
        ptSessionsOptions={ptSessionsOptions}
      />
    </div>
  );
}
