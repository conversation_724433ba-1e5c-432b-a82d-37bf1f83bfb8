"use client";

import { useForm<PERSON>ontext } from "react-hook-form";
import {
  FormField,
  FormItem,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

import { ToggleProps, Toggles } from "@/components/ui/toggle-group";
import { string, z } from "zod";
import { Label } from "@/components/ui/label";

const phoneRegex = new RegExp(
  /^([+]?[\s0-9]+)?(\d{3}|[(]?[0-9]+[)])?([-]?[\s]?[0-9])+$/
);

const emailRegex = new RegExp(
  /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|.(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
);

export const TrainerRequestSchema = z.object({
  first_name: z.string({
    required_error: "First name is required",
  }),
  last_name: z.string({
    required_error: "Last name is required",
  }),
  email: z
    .string({
      required_error: "Email is required",
    })
    .regex(emailRegex, "Email is not valid"),
  phone_number: z
    .string({
      required_error: "Phone number is required",
    })
    .max(11, { message: "Phone number is not valid" })
    .regex(phoneRegex, "Phone number is not valid"),
  gym_id: z.object(
    {
      value: z.string(),
      label: z.string(),
    },
    { required_error: "Location is required" }
  ),
  category_id: z.object(
    {
      value: z.string(),
      label: z.string(),
    },
    { required_error: "Category is required" }
  ),
  session_id: z.object(
    {
      value: z.string(),
      label: z.string(),
    },
    { required_error: "Type is required" }
  ),
  training_days: string({
    required_error: "Training day(s) is required",
  })
    .array()
    .min(1, { message: "Must select at least 1 day of the week" }),

  training_per_week: string({
    required_error: "Training per week is required",
  }).min(1, { message: "Training per week is required" }),

  availability: string({
    required_error: "Availability is required",
  })
    .array()
    .min(1, { message: "Must select at least 1 time slot" }),

  focus_area: string({
    required_error: "Focus area is required",
  })
    .array()
    .min(1, { message: "Must select at least 1 specialty" }),

  gender_pref: string({
    required_error: "Gender is required",
  }).min(1, { message: "Must select a gender" }),
});

export type FormDataType = z.infer<typeof TrainerRequestSchema>;

interface FormInputProps {
  name: string;
  placeholder: string;
  type?: string;
  label?: string;
}

export const FormInput: React.FC<FormInputProps> = ({
  name,
  placeholder,
  type,
  label,
}) => {
  const { control } = useFormContext();
  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem>
          <FormControl>
            <>
              <Label htmlFor={label}>{label}</Label>
              <Input
                className='text-base w-full'
                id={label}
                placeholder={placeholder}
                {...field}
                type={type}
              />
            </>
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
};

interface FormToggleProps extends ToggleProps {
  question: string;
  name: string;
  className?: string;
}

export const FormToggle: React.FC<FormToggleProps> = ({
  question,
  type,
  lists,
  className,
  name,
}) => {
  const { control } = useFormContext();

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <div className='flex flex-col gap-4 mt-6 mb-6'>
          <p className='pr-2'>{question}</p>
          <FormItem>
            <FormControl>
              <>
                <Toggles
                  {...field}
                  type={type}
                  lists={lists}
                  className={className}
                />
              </>
            </FormControl>
            <FormMessage />
          </FormItem>
        </div>
      )}
    />
  );
};
