"use client";
import { Form } from "@/components/ui/form";
import { But<PERSON> } from "@/components/ui/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { FormProvider, useForm } from "react-hook-form";
import {
  FormDataType,
  FormInput,
  FormToggle,
  TrainerRequestSchema,
} from "./field-helpers";
import { z } from "zod";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { Option } from "@/components/ui/select";
import { useRequestTrainer } from "../mutations/useRequestTrainer";
import { sanitizeData } from "./utils";
import { Loader2 } from "lucide-react";
import { useMemo, useState } from "react";
import { ALL_OPTION } from "@/components/custom/activity-filters";
import { FormSelect } from "@/components/custom/form-select";

export function RequestTrainerForm({
  facilitiesOptions,
  ptCategoriesOptions,
  ptSessionsOptions,
}: Readonly<{
  facilitiesOptions: Option[];
  ptCategoriesOptions: Option[];
  ptSessionsOptions: Option[];
}>) {
  const params = useParams();

  const router = useRouter();

  const trainerId = useSearchParams().get("trainerId") ?? "";

  // Add a state to track form resets and use it as a key to force re-render
  const [formResetKey, setFormResetKey] = useState(0);

  const form = useForm<z.infer<typeof TrainerRequestSchema>>({
    resolver: zodResolver(TrainerRequestSchema),
    defaultValues: {
      first_name: "",
      last_name: "",
      email: "",
      phone_number: "",
      gym_id: {
        value: "",
        label: "",
      },
      category_id: {
        value: "",
        label: "",
      },
      session_id: {
        value: "",
        label: "",
      },
      training_days: [],
      training_per_week: "",
      availability: [],
      focus_area: [],
      gender_pref: "",
    },
  });

  const { mutate: handleRequestTrainer, isPending } = useRequestTrainer(() => {
    // Create a completely new form instance with default values
    const defaultValues = {
      first_name: "",
      last_name: "",
      email: "",
      phone_number: "",
      gym_id: {
        value: "",
        label: "",
      },
      category_id: {
        value: "",
        label: "",
      },
      session_id: {
        value: "",
        label: "",
      },
      training_days: [],
      training_per_week: "",
      availability: [],
      focus_area: [],
      gender_pref: "",
    };

    // Reset the form with keepDefaultValues: false to ensure a complete reset
    form.reset(defaultValues, {
      keepDefaultValues: false,
      keepDirty: false,
      keepErrors: false,
      keepDirtyValues: false,
      keepIsSubmitted: false,
      keepTouched: false,
      keepIsValid: false,
      keepSubmitCount: false,
    });

    if (trainerId) {
      router.push(`/personal-training/${params?.orgId}`);
    }
  });

  function onSubmit(data: FormDataType) {
    const sanitizedData = sanitizeData(data, String(params?.orgId), trainerId);
    handleRequestTrainer(sanitizedData);

    // Reset form immediately after submission as well
    // This provides an additional layer of assurance that the form is reset
    const defaultValues = {
      first_name: "",
      last_name: "",
      email: "",
      phone_number: "",
      gym_id: {
        value: "",
        label: "",
      },
      category_id: {
        value: "",
        label: "",
      },
      session_id: {
        value: "",
        label: "",
      },
      training_days: [],
      training_per_week: "",
      availability: [],
      focus_area: [],
      gender_pref: "",
    };

    // Reset with all options to ensure complete reset
    form.reset(defaultValues, {
      keepDefaultValues: false,
      keepDirty: false,
      keepErrors: false,
      keepDirtyValues: false,
      keepIsSubmitted: false,
      keepTouched: false,
      keepIsValid: false,
      keepSubmitCount: false,
    });

    // Increment the form reset key to force a complete re-render of the form
    setFormResetKey(prevKey => prevKey + 1);
  }

  const categoryId = form.watch("category_id");
  const location = form.watch("gym_id");

  const filteredFacilitiesOptions = useMemo(
    () => facilitiesOptions.filter(option => option.value !== ALL_OPTION),
    [facilitiesOptions]
  );
  const filteredPtCategoriesOptions = useMemo(
    () => ptCategoriesOptions.filter(option => option.value !== ALL_OPTION),
    [ptCategoriesOptions]
  );

  const filteredPtSessionsOptions = useMemo(
    () =>
      ptSessionsOptions.filter(
        option =>
          String(option?.gym_id) === location?.value &&
          String(option?.category_id) === categoryId?.value
      ),
    [categoryId?.value, location?.value, ptSessionsOptions]
  );

  return (
    <div key={formResetKey}>
      <FormProvider {...form}>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className='grid lg:grid-cols-2 gap-4 mt-20 grid-cols-1 mr-2'>
              <FormInput
                name='first_name'
                placeholder='First Name'
                label='First Name'
              />
              <FormInput
                name='last_name'
                placeholder='Last Name'
                label='Last Name'
              />
              <FormInput
                name='email'
                type='email'
                placeholder='Email'
                label='Email'
              />
              <FormInput
                name='phone_number'
                placeholder='Phone'
                type='tel'
                label='Phone'
              />
            </div>
            <div className='flex lg:flex-row gap-4 mt-6 flex-col mr-2'>
              <FormSelect
                name='gym_id'
                placeholder='Location'
                options={filteredFacilitiesOptions}
              />
              <FormSelect
                name='category_id'
                placeholder='Category'
                options={filteredPtCategoriesOptions}
              />
              <FormSelect
                name='session_id'
                placeholder='Select type'
                options={filteredPtSessionsOptions}
                disabled={!categoryId?.value || !location?.value}
              />
            </div>
            <div className='mt-8'>
              <FormToggle
                name='training_days'
                className='grid grid-cols-2 w-full gap-3 lg:flex'
                question='What is your preferred day(s) of the week to meet?'
                type='multiple'
                lists={[
                  { label: "Sunday", value: "Sunday" },
                  { label: "Monday", value: "Monday" },
                  { label: "Tuesday", value: "Tuesday" },
                  { label: "Wednesday", value: "Wednesday" },
                  { label: "Thursday", value: "Thursday" },
                  { label: "Friday", value: "Friday" },
                  { label: "Saturday", value: "Saturday" },
                ]}
              />
              <FormToggle
                name='training_per_week'
                className='grid grid-cols-4 w-full gap-3 lg:flex'
                question='How many times per week would you like to meet?'
                lists={[
                  { label: "1", value: "1" },
                  { label: "2", value: "2" },
                  { label: "3", value: "3" },
                  { label: "4", value: "4" },
                  { label: "5", value: "5" },
                  { label: "6", value: "6" },
                  { label: "7", value: "7" },
                ]}
              />
              <FormToggle
                name='availability'
                type='multiple'
                className='grid grid-cols-2 w-full gap-3 lg:flex'
                question='When is your preferred time of day to meet?'
                lists={[
                  { label: "6:00am-9:00 am", value: "6:00am-9:00 am" },
                  { label: "9:00am-12:00 pm", value: "9:00am-12:00 pm" },
                  { label: "12:00pm-5:00 pm", value: "12:00pm-5:00 pm" },
                  { label: "5:00pm-9:00 pm", value: "5:00pm-9:00 pm" },
                ]}
              />
              <FormToggle
                name='focus_area'
                className='grid grid-cols-2 w-full gap-3 lg:flex'
                question='What would you like to focus on during your sessions?'
                type='multiple'
                lists={[
                  { label: "Weight Management", value: "Weight Management" },
                  {
                    label: "Sports Conditioning",
                    value: "Sports Conditioning",
                  },
                  { label: "Strength Training", value: "Strength Training" },
                  { label: "Running/Marathon", value: "Running/Marathon" },
                  {
                    label: "Functional Training",
                    value: "Functional Training",
                  },
                  { label: "Mobility Training", value: "Mobility Training" },
                ]}
              />
              <FormToggle
                name='gender_pref'
                question='Gender of Trainer'
                lists={[
                  { label: "Any", value: "any" },
                  { label: "Male", value: "Male" },
                  { label: "Female", value: "Female" },
                ]}
              />
            </div>

            <div className='flex'>
              <Button
                disabled={isPending}
                type='submit'
                className='text-white lg:w-[200px] w-[90px]'
              >
                {isPending && <Loader2 className='mr-2 h-4 w-4 animate-spin' />}
                SUBMIT
              </Button>
              <Link href={`/personal-training/${params?.orgId}`}>
                <Button
                  variant={"secondary"}
                  className='lg:w-[200px] ml-4 w-full !text-black'
                >
                  Cancel
                </Button>
              </Link>
            </div>
          </form>
        </Form>
      </FormProvider>
    </div>
  );
}
