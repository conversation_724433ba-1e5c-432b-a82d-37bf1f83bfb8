"use client";

import { useApplyStateToUrl } from "@/common/hooks/useApplyStateToUrl";
import { ALL_OPTION } from "@/components/custom/activity-filters";
import { BaseSelect, Option } from "@/components/ui/select";
import { useSearchParams } from "next/navigation";

export const PersonalFilters = ({
  locations,
  specialties,
  ptCategoriesOptions,
  ptSessionsOptions,
}: {
  locations: Option[];
  specialties: Option[];
  ptCategoriesOptions: Option[];
  ptSessionsOptions: Option[];
}) => {
  const applyToUrl = useApplyStateToUrl();

  const location = useSearchParams().get("gym_ids");
  const category = useSearchParams().get("category_id");
  const type = useSearchParams().get("session_id");
  const specialty = useSearchParams().get("specialty_ids")?.split(",");
  const experience = useSearchParams().get("experience");
  const gender = useSearchParams().get("gender");

  const handleChange = (option: Option | Option[], identify: string) => {
    if (Array.isArray(option)) {
      if (option.some(opt => opt.value === ALL_OPTION)) {
        applyToUrl({ [identify]: null });
        return;
      }
      return applyToUrl({ [identify]: option.map(val => val.value).join(",") });
    } else {
      if (option?.value === ALL_OPTION) {
        applyToUrl({ [identify]: null });
        return;
      }
      return applyToUrl({ [identify]: option?.value });
    }
  };

  return (
    <div className='grid grid-cols-1 lg:grid-cols-3 gap-4 mb-16 lg:mr-10'>
      <BaseSelect
        className='w-full'
        onChange={val => handleChange(val, "gym_ids")}
        placeholder='Location'
        options={locations}
        value={location ?? ""}
      />
      <BaseSelect
        className='w-full'
        onChange={val => handleChange(val, "category_id")}
        placeholder='Category'
        options={ptCategoriesOptions}
        value={category ?? ""}
        multiple
      />
      <BaseSelect
        className='w-full'
        onChange={val => handleChange(val, "session_id")}
        placeholder='Type'
        options={ptSessionsOptions}
        value={type ?? ""}
      />
      <BaseSelect
        className='w-full'
        onChange={val => handleChange(val, "specialty_ids")}
        placeholder='Specialty'
        options={specialties}
        value={specialty ?? ""}
        multiple
        closeMenuOnSelect={false}
      />
      <BaseSelect
        className='w-full'
        onChange={val => handleChange(val, "experience")}
        placeholder='Experience'
        options={[
          {
            label: "All Experience Levels",
            value: ALL_OPTION,
          },
          { label: "0 - 5 years", value: "0,5" },
          { label: "5 - 10 years", value: "5,10" },
          { label: "10 - 20 years", value: "10,20" },
          { label: "20 - 30 years", value: "20,30" },
        ]}
        value={experience ?? ""}
      />
      <BaseSelect
        className='w-full'
        onChange={val => handleChange(val, "gender")}
        placeholder='Gender'
        options={[
          { label: "All", value: ALL_OPTION },
          { label: "Male", value: "male" },
          { label: "Female", value: "female" },
        ]}
        value={gender ?? ""}
      />
    </div>
  );
};
