export type TrainerType = {
  bio: string;
  email: string;
  first_name: string;
  gender: string;
  gym_id: number;
  gym_name: string;
  id: number;
  images: string[];
  last_name: string;
  primary_role: number;
  specialties: { id: string; name: string }[];
  user_images: string[];
  years_experience: number;
};

export type LinkType = {
  url: string | null;
  label: string;
  active: boolean;
};

export type TrainerData = {
  current_page: number;
  data: TrainerType[];
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  links: LinkType[];
  next_page_url: string;
  path: string;
  per_page: number;
  prev_page_url: string | null;
  to: number;
  total: number;
};
