"use client";

import { DesktopModal } from "./desktop-modal";
import { MobileModal } from "./mobile-modal";
import { Dispatch, SetStateAction, useEffect, useState } from "react";
import { TrainerType } from "../types";
import { useDeviceDetect } from "@/common/hooks/useDeviceDetect";

export interface DetailModalProps {
  isOpen: boolean;
  setIsOpen?: Dispatch<SetStateAction<TrainerType | null>>;
  data?: TrainerType;
}

export const DetailModal = (props: DetailModalProps) => {
  const { isMobileOnly } = useDeviceDetect();
  const [mounted, setMounted] = useState(false);

  // Only render after component is mounted on the client
  useEffect(() => {
    setMounted(true);
  }, []);

  // Return null during server-side rendering or before mounting
  if (!mounted) {
    return null;
  }

  if (isMobileOnly) {
    return <MobileModal {...props} />;
  }

  return <DesktopModal {...props} />;
};
