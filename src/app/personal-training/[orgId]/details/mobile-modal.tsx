import { Dialog, DialogContent, DialogHeader } from "@/components/ui/dialog";
import { DetailModalProps } from "./details";
import Image from "next/image";
import { BioComponent, imageLoader } from "./desktop-modal";
import { Button } from "@/components/ui/button";
import { styled } from "styled-components";
import tw from "twin.macro";
import { useStoreValue } from "@/app/StoreContext";
import Link from "next/link";
import { useParams } from "next/navigation";

const SpecialtyText = styled.h2<{ color?: string }>`
  ${tw`text-lg font-semibold`};
  color: ${({ color }) => color};
`;

const StyledHeader = styled(DialogHeader)<{ color?: string }>`
  ${tw`bg-[#00a1c5] p-4 pt-0 flex flex-col`};
  background-color: ${({ color }) => color};
`;

export const MobileModal = ({ isOpen, setIsOpen, data }: DetailModalProps) => {
  const { state: configs } = useStoreValue(state => state.configs);

  const params = useParams();
  return (
    <Dialog open={isOpen} onOpenChange={() => setIsOpen?.(null)}>
      <DialogContent
        className='w-full p-0 m-0 border-0 h-full overflow-x-scroll'
        color='white'
      >
        <div className='bg-white border-0 border-gray-200 shadow-md'>
          <StyledHeader color={configs?.accent_color}>
            <Image
              className='rounded-lg pt-8 object-fit object-center h-52'
              src={data?.images?.[0] ?? imageLoader}
              height={190}
              width={190}
              alt='upace-image'
              priority
            />
            <h2 className='text-lg font-bold text-white mb-2 text-left'>
              {`${data?.first_name} ${data?.last_name}`}
            </h2>
          </StyledHeader>

          <div className='p-4'>
            <p className='mt-2 '>Personal Trainer</p>
            <p> {data?.gym_name}</p>
            <p>{`${data?.years_experience} Years Experience`}</p>
            <hr className='my-4' />
            {Boolean(data?.specialties.length) && (
              <>
                <SpecialtyText color={configs?.accent_color}>
                  Specialties
                </SpecialtyText>
                <ul className='list-disc pl-6 mt-2 text-gray-700'>
                  {data?.specialties?.map(spec => (
                    <li className='mb-2' key={spec?.id}>
                      {spec?.name}
                    </li>
                  ))}
                </ul>
              </>
            )}

            <div className='mt-4'>
              <BioComponent bio={data?.bio} />
            </div>

            <Link
              href={`${params?.orgId}/request-trainer?trainerId=${data?.id}`}
            >
              <Button className='mt-4'>Request Info</Button>
            </Link>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
