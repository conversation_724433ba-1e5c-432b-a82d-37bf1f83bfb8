import { Button } from "@/components/ui/button";
import {
  Dialog,
  <PERSON><PERSON><PERSON>lose,
  <PERSON><PERSON><PERSON>ontent,
  DialogHeader,
} from "@/components/ui/dialog";
import Image from "next/image";
import { DetailModalProps } from "./details";
import { styled } from "styled-components";
import tw from "twin.macro";
import { useStoreValue } from "@/app/StoreContext";
import Link from "next/link";
import { useParams } from "next/navigation";

export const imageLoader =
  "https://placehold.co/200x200?text=User+Image+Not+Found";

export const BioComponent = ({ bio }: { bio?: string }) => {
  return <div dangerouslySetInnerHTML={{ __html: bio ?? "" }} />;
};

const SpecialtyText = styled.h2<{ color?: string }>`
  ${tw`text-lg font-semibold text-lightBlue-300`};
  color: ${({ color }) => color};
`;

const StyledHeader = styled(DialogHeader)<{ color?: string }>`
  ${tw`w-full`};
  background-color: ${({ color }) => color};
`;

const Specialties = ({
  specialties,
  color,
}: {
  specialties?: { id: string; name: string }[];
  color?: string;
}) => {
  return (
    <div className='lg:w-2/5'>
      <div className='mt-4'>
        {Boolean(specialties?.length) && (
          <>
            <SpecialtyText color={color}>Specialties</SpecialtyText>
            <ul className='list-disc pl-6 mt-2 text-gray-700'>
              {specialties?.map(spec => (
                <li className='mb-2 whitespace-nowrap' key={spec?.id}>
                  {spec?.name}
                </li>
              ))}
            </ul>
          </>
        )}
      </div>
    </div>
  );
};

export const DesktopModal = ({ isOpen, setIsOpen, data }: DetailModalProps) => {
  const { state: configs } = useStoreValue(state => state.configs);
  const params = useParams();

  return (
    <Dialog open={isOpen} onOpenChange={() => setIsOpen?.(null)}>
      <DialogContent
        className='lg:max-w-[800px] w-full p-0 m-0 border-0'
        color='white'
      >
        <StyledHeader color={configs?.accent_color}>
          <div className='p-6 relative text-white mt-4 mx-4 w-1/2'>
            <h1 className='text-2xl font-semibold'>{`${data?.first_name} ${data?.last_name}`}</h1>
            <p className='text-sm mt-2'>
              <span>{`Personal Trainer | ${data?.gym_name} |`}</span>
              <span className='pl-1 whitespace-nowrap'>{`${data?.years_experience} Years Experience`}</span>
            </p>
          </div>
          <DialogClose
            className='!absolute top-0 right-0 m-4 !bg-white !text-white'
            color='white'
          />
        </StyledHeader>

        <div className='bg-white p-2  mx-6'>
          <div className='flex flex-col lg:flex-row gap-10'>
            <div className='lg:w-3/5'>
              <div className='text-gray-800 space-y-4'>
                <BioComponent bio={data?.bio} />
              </div>
              <Link
                href={`${params?.orgId}/request-trainer?trainerId=${data?.id}`}
              >
                <Button className='mt-4 rounded'>Request Info</Button>
              </Link>
            </div>
            <div>
              <Image
                className='inset-0 rounded-md object-contain h-52 w-[90%] mt-[-7.2rem]'
                src={data?.images?.[0] ?? imageLoader}
                height={190}
                width={190}
                alt='upace-image'
                priority
              />
              <Specialties
                specialties={data?.specialties}
                color={configs?.accent_color}
              />
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
