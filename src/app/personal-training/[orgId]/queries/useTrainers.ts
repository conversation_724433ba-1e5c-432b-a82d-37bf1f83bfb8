"use client";

import { useQuery } from "@tanstack/react-query";

import { BASE_API_URL_CLIENT } from "@/app/classes/[orgId]/actions/constant";
import { TrainerData } from "../types";

export const fetchTrainers = async (
  orgId: string,
  params: Record<string, string | number>,
  signal?: AbortSignal
): Promise<TrainerData> => {
  const headers: { "Content-Type": string; authorization?: string } = {
    "Content-Type": "application/json",
  };

  const trainersURL = `${BASE_API_URL_CLIENT}/trainers/unauth`;

  try {
    const urlParams = new URLSearchParams({
      ...params,
      university_id: orgId,
      page: params?.page?.toString() || "1",
    }).toString();

    const rec = await fetch(`${trainersURL}?${urlParams}&per_page=20`, {
      headers,
      signal,
    });

    const data = await rec.json();

    return data?.trainers || [];
  } catch (err) {
    throw new Error("Could not fetch trainers");
  }
};

export const useTrainers = (
  orgId: string,
  searchParams: Record<string, string | number>
) => {
  return useQuery({
    queryKey: [orgId, searchParams],
    queryFn: async ({ signal }) => fetchTrainers(orgId, searchParams, signal),
  });
};
