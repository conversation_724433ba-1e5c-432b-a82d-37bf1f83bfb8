"use client";

import { BASE_API_URL_CLIENT } from "@/app/classes/[orgId]/actions/constant";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";

const requestTrainer = async (data: Record<string, unknown>) => {
  const headers: { "Content-Type": string; authorization?: string } = {
    "Content-Type": "application/json",
  };

  try {
    const response = await fetch(
      `${BASE_API_URL_CLIENT}/trainers/requestinfo/unauth`,
      {
        headers,
        method: "POST",
        body: JSON.stringify(data),
      }
    );
    return await response.json();
  } catch (err) {
    throw new Error("Sorry, there was a problem requesting a trainer");
  }
};

export const useRequestTrainer = (onSuccess?: () => void) => {
  return useMutation({
    mutationFn: requestTrainer,

    onSuccess: async data => {
      if (data.success) {
        onSuccess?.();
        return toast.success(data?.message);
      }

      return toast.error(data?.message);
    },

    onError: e => {
      return toast.error(e?.message);
    },
  });
};
