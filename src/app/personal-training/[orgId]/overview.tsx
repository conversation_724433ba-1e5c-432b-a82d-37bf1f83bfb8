"use client";

import { isEmpty } from "lodash/fp";
import EmptyState from "./empty-state";
import { Loader } from "./loading-skeleton";

import { useTrainers } from "./queries/useTrainers";
import { Trainers } from "./trainers";

export const Overview = ({
  orgId,
  searchParams,
}: Readonly<{
  orgId: string;
  searchParams: Record<string, string | number>;
}>) => {
  const { data, isPending } = useTrainers(orgId, searchParams);

  if (isPending) {
    return <Loader />;
  }

  if (!isPending && isEmpty(data?.data)) {
    return <EmptyState />;
  }

  return <Trainers trainers={data?.data || []} />;
};
