/* eslint-disable @typescript-eslint/ban-ts-comment */
import { Button } from "@/components/ui/button";
import Image from "next/image";

import { useState } from "react";
import { TrainerType } from "./types";
import { DetailModal } from "./details/details";
import { styled } from "styled-components";
import tw from "twin.macro";
import { useStoreValue } from "@/app/StoreContext";

const imageLoader = "https://placehold.co/200x200?text=User+Image+Not+Found";

const StyledHeader = styled.h2<{ color?: string }>`
  ${tw`font-bold mb-1`};
  color: ${({ color }) => color};
`;

const StyledName = styled.h3<{ color?: string }>`
  ${tw`text-lg font-semibold text-lightBlue-300 whitespace-break-spaces`};
  color: ${({ color }) => color};
`;

const TrainerCard = ({
  user,
  specialties,
  onCardClick,
}: {
  user: TrainerType;
  specialties: TrainerType["specialties"];
  onCardClick?: () => void;
}) => {
  const { state: configs } = useStoreValue(state => state.configs);

  return (
    <div
      onClick={onCardClick}
      className='bg-white rounded-lg flex flex-col cursor-pointer transition-shadow duration-200 h-full w-full lg:w-64 hover:shadow-lg'
    >
      <Image
        className='inset-0  z-3 rounded object-contain h-52 w-full'
        src={String(user?.images?.[0]) ?? imageLoader}
        height={260}
        width={260}
        alt='upace-image'
        priority
      />
      <div className='pt-5 flex flex-col justify-between pl-2 pb-2 flex-grow'>
        <StyledName
          color={configs?.accent_color}
        >{`${user.first_name} ${user.last_name}`}</StyledName>
        {Boolean(specialties?.length) && (
          <div className='text-sm text-gray-600 mt-2 mb-2'>
            <StyledHeader color={configs?.accent_color}>
              Specialties
            </StyledHeader>
            <ul className='list-disc pl-5'>
              {specialties?.slice(0, 4).map(spec => (
                <li className='mb-2' key={spec?.id}>
                  {spec?.name}
                </li>
              ))}
              {specialties?.length > 4 && <li>More...</li>}
            </ul>
          </div>
        )}
        <div className='mt-auto'>
          <Button className='mt-4 rounded-md w-20'>About</Button>
        </div>
      </div>
    </div>
  );
};

export const Trainers = ({ trainers = [] }: { trainers?: TrainerType[] }) => {
  const [selected, setSelected] = useState<TrainerType | null>(null);

  return (
    <>
      <div className='grid grid-cols-1 lg:grid-cols-4 gap-6 ml-1'>
        {trainers.map(trainer => (
          <TrainerCard
            //@ts-expect-error
            key={trainer.id}
            user={trainer}
            specialties={trainer?.specialties}
            onCardClick={() => setSelected(trainer)}
          />
        ))}
      </div>
      {selected && (
        <DetailModal
          data={selected}
          isOpen={Boolean(selected)}
          setIsOpen={setSelected}
        />
      )}
    </>
  );
};
