"use client";
/* eslint-disable @typescript-eslint/ban-ts-comment */
import { useApplyStateToUrl } from "@/common/hooks/useApplyStateToUrl";
import {
  PaginationContent,
  PaginationItem,
  PaginationLink,
} from "@/components/ui/pagination";
import { useTrainers } from "./queries/useTrainers";
import { Fragment } from "react";
import { filter, flow, isEmpty, uniqBy } from "lodash/fp";
import { LinkType } from "./types";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { styled } from "styled-components";
import { useStoreValue } from "@/app/StoreContext";
import { useSearchParams } from "next/navigation";

const StyledPaginationItem = styled(PaginationLink)<{ color?: string }>`
  color: ${({ color }) => color};
`;

export const Pagination = ({
  orgId,
  searchParams,
}: {
  orgId: string;
  searchParams: Record<string, string | number>;
}) => {
  const { data } = useTrainers(orgId, searchParams);

  const applyToUrl = useApplyStateToUrl();

  const paginationLinks = flow(
    filter((link: LinkType) => link.url !== null),
    uniqBy("url")
  )(data?.links || []);

  const { state: configs } = useStoreValue(state => state.configs);

  const page = useSearchParams().get("page") ?? 1;

  if (isEmpty(data?.data) || (data?.total ?? 0) < 20) {
    return null;
  }

  return (
    <PaginationContent className='flex items-center justify-center p-4 mt-4 mb-4 sticky bottom-0 bg-white sm:pl-1s'>
      {paginationLinks?.map((link: LinkType, index: number) => {
        const lastIndex = paginationLinks?.length - 1;
        const firstIndex = index === 0;

        return (
          //@ts-expect-error
          <Fragment key={index}>
            {firstIndex && (
              <ChevronLeft
                onClick={() => {
                  const number = Number(page) !== 1 ? Number(page) - 1 : 1;
                  applyToUrl({ page: number });
                }}
                color={configs?.button_color}
                cursor={"pointer"}
                size={30}
              />
            )}
            <PaginationItem key={index}>
              <StyledPaginationItem
                onClick={e => {
                  e.preventDefault();
                  applyToUrl({ page: index + 1 });
                }}
                isActive={link.active}
                href={String(link?.url)}
                color={link.active ? configs?.button_color : ""}
              >
                {index + 1}
              </StyledPaginationItem>
            </PaginationItem>
            {index === lastIndex && (
              <ChevronRight
                cursor={"pointer"}
                color={configs?.button_color}
                size={30}
                onClick={() => {
                  const number =
                    Number(page) !== lastIndex + 1
                      ? Number(page) + 1
                      : paginationLinks?.length;
                  applyToUrl({ page: number });
                }}
              />
            )}
          </Fragment>
        );
      })}
    </PaginationContent>
  );
};
