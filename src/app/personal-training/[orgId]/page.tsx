import { fetchClientConfigs } from "@/common/api/fetchClientConfigs";
import { PersonalFilters } from "./filters";

import { Overview } from "./overview";
import { Pagination } from "./pagination";

export default async function PTPage({
  params,
  searchParams,
}: Readonly<{
  params: {
    orgId: string;
  };
  searchParams: Record<string, string | number>;
}>) {
  const {
    facilitiesOptions,
    specialtiesOptions,
    ptCategoriesOptions,
    ptSessionsOptions,
  } = await fetchClientConfigs(params.orgId);

  return (
    <div className='flex flex-col  mt-5 lg:mt-20 pl-8 pr-8 container'>
      <div>
        <PersonalFilters
          locations={facilitiesOptions}
          specialties={specialtiesOptions}
          ptCategoriesOptions={ptCategoriesOptions}
          ptSessionsOptions={ptSessionsOptions}
        />
        <Overview orgId={params.orgId} searchParams={searchParams} />
        <Pagination orgId={params.orgId} searchParams={searchParams} />
      </div>
    </div>
  );
}
