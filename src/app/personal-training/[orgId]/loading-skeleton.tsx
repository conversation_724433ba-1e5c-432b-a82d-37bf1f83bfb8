/* eslint-disable @typescript-eslint/ban-ts-comment */
import { Skeleton } from "@/components/ui/skeleton";

export const SkeletonLoader = () => {
  return (
    <div className='flex flex-col space-y-3'>
      <Skeleton className='h-[200px] w-full rounded-xl bg-gray-300' />
      <div className='space-y-2'>
        <Skeleton className='h-4 w-full bg-gray-300' />
        <Skeleton className='h-4 w-full bg-gray-300' />
        <Skeleton className='h-4 w-[150px] bg-gray-300' />
        <Skeleton className='h-4 w-[80px] bg-gray-300' />
      </div>
    </div>
  );
};

export const Loader = () => {
  return (
    <div className='grid grid-cols-1  lg:grid-cols-4 gap-6 ml-1 cursor-progress'>
      {Array.from({ length: 12 }, (_, index) => (
        //@ts-expect-error
        <SkeletonLoader key={index} />
      ))}
    </div>
  );
};
