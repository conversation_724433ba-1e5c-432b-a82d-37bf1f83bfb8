"use client";

import { BASE_API_URL_CLIENT } from "@/app/classes/[orgId]/actions/constant";
import { useQuery } from "@tanstack/react-query";
import { EquipmentWeeklyType } from "../types";

export const fetchWeeklyEquipment = async (
  orgId: string,
  params: Record<string, string | number>,
  xSource?: string
): Promise<EquipmentWeeklyType> => {
  try {
    const headers: {
      "X-SOURCE": string;
    } = {
      "X-SOURCE": xSource ?? "SCHEDULE",
    };

    const urlParams = new URLSearchParams({
      ...params,
      university_id: orgId,
    }).toString();
    const response = await fetch(
      `${BASE_API_URL_CLIENT}/equipment/schedules/unauth?${urlParams}&exclude_if_closed_or_cancelled=true&exclude_past_classes=true`,
      { headers }
    );
    return await response.json();
  } catch (err) {
    throw new Error("Sorry, there was an error fetching the data.");
  }
};

export const useEquipmentWeeklyData = (
  orgId: string,
  searchParams: Record<string, string | number>,
  enabledRequest: boolean = false,
  xSource?: string
) => {
  return useQuery({
    queryKey: [orgId, searchParams],
    enabled: enabledRequest,
    refetchOnWindowFocus: false,
    refetchInterval: false,
    select: data => data?.schedule,
    queryFn: () => fetchWeeklyEquipment(orgId, searchParams, xSource),
  });
};
