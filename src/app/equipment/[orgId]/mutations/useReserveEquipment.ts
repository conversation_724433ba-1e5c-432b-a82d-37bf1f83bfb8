"use client";

import { reserveAction } from "@/app/actions/reserve";
import { useOptimisticUpdate } from "@/app/classes/[orgId]/mutations/useOptimisticUpdate";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";

export const useReserveEquipment = (onSuccess?: (data?: unknown) => void) => {
  const invalidateClassesQueries = useOptimisticUpdate();

  return useMutation({
    mutationFn: reserveAction,
    onSuccess: async data => {
      await invalidateClassesQueries();

      if (data.success) {
        if (data?.data?.reservation_id) {
          onSuccess?.(data.data);
        }
        return toast.success(data?.message);
      }

      return toast.error(data?.message);
    },

    onError: e => toast.error(e?.message),
  });
};
