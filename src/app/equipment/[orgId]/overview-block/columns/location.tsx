"use client";
import { EquipmentProps } from "../../types";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { useState } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";
import { styled } from "styled-components";
import { useStoreValue } from "@/app/StoreContext";
import { z } from "zod";
import { FormSelect } from "@/components/custom/form-select";
import { ReserveButton } from "./action";
import { ReserVationView } from "./reserve-view";

const HeaderText = styled.p<{ color?: string }>`
  color: ${props => props.color || "black"};
`;

const IconUp = styled(ChevronUp)`
  color: ${props => props.color || "blue"};
`;

const IconDown = styled(ChevronDown)`
  color: ${props => props.color || "blue"};
`;

export const EquipmentReserveSchema = z.object({
  time: z.object(
    {
      value: z.string(),
      label: z.string(),
    },
    { required_error: "Time is required" }
  ),
  duration: z.object(
    {
      value: z.number(),
      label: z.string(),
    },
    { required_error: "Duration is required" }
  ),
  attending_persons: z.object(
    {
      value: z.number(),
      label: z.string(),
    },
    { required_error: "Persons attending is/are required" }
  ),
});

export type EquipmentFormType = z.infer<typeof EquipmentReserveSchema>;

export const LocationColumn = ({ data }: { data: EquipmentProps }) => {
  const [isOpen, setIsOpen] = useState(false);

  const { state: configs } = useStoreValue(state => state.configs);

  const startTimeOptions = data.available_time_slots?.map(slot => ({
    value: slot?.time_value,
    label: `${slot.time_label} - (${slot?.remarks})`,
  }));

  if (data?.current_user_reservation) {
    return (
      <ReserVationView
        id={Number(data.current_user_reservation.id)}
        startTime={String(data.current_user_reservation.start_time)}
        endTime={String(data.current_user_reservation.end_time)}
        attendingPerson={String(
          data.current_user_reservation.attending_persons
        )}
        name={`${data.name}-${data.gym_name}`}
      />
    );
  }

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen} className='w-full mt-4'>
      <CollapsibleTrigger className='w-full max-w-max flex'>
        {isOpen ? (
          <IconUp color={configs?.accent_color} />
        ) : (
          <IconDown color={configs?.accent_color} />
        )}
        <HeaderText
          color={isOpen ? configs?.accent_color : ""}
          className={"pl-4"}
        >
          {`${data.name}-${data.gym_name}`}
        </HeaderText>
      </CollapsibleTrigger>

      <CollapsibleContent className='mt-6 mb-4'>
        <div className='flex flex-col gap-4 md:ml-8 lg:ml-8 mb:items-center lg:items-center  lg:flex-row md:flex-row'>
          <FormSelect
            wrapperClassName='flex-col gap-2'
            name='time'
            placeholder='Start Time'
            options={startTimeOptions}
            className='w-full'
            label='Start Time:'
          />

          <FormSelect
            wrapperClassName='flex-col gap-2'
            placeholder='Duration'
            options={data?.durations || []}
            label='Duration:'
            name='duration'
            className='w-full'
          />
          <FormSelect
            wrapperClassName='flex-col gap-2'
            placeholder='People attending'
            options={data?.attending_persons || []}
            label='People attending:'
            name='attending_persons'
            className='w-full'
          />

          <div className='lg:hidden md:hidden flex w-full'>
            <ReserveButton data={data} />
          </div>
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
};
