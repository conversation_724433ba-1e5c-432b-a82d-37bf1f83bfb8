"use client";

import { ColumnDef } from "@tanstack/react-table";

import { useMemo } from "react";

import { SortArrow } from "@/components/custom/column-header-sort";

import { LocationColumn } from "./location";
import { ColumnBadge, DefaultStatus } from "@/components/custom/column-badge";
import { ReserveButton } from "./action";
import { EquipmentProps } from "../../types";

const getStatus = (text: string) => {
  switch (text?.toUpperCase()) {
    case "FULL":
      return DefaultStatus.FULL;
    default:
      return undefined;
  }
};

export const useDesktopColumns = () => {
  return useMemo<ColumnDef<EquipmentProps>[]>(
    () => [
      {
        accessorKey: "name",
        header: "LOCATION",
        cell: ({ row }) => <LocationColumn data={row.original} />,
        size: 500,
      },
      {
        accessorKey: "status",
        header: "STATUS",
        cell: ({ row }) => (
          <ColumnBadge
            status={row.original as unknown as Record<string, string>}
            getStatus={
              row.original?.status
                ? () => getStatus(String(row.original?.status))
                : undefined
            }
          />
        ),
      },
      {
        accessorFn: row => row?.next_available_time_slot?.time_label,
        id: "next_available_time_slot",
        header: ({ column }) => {
          return (
            <SortArrow
              onClick={() =>
                column.toggleSorting(column.getIsSorted() === "asc")
              }
              sortDirection={column.getIsSorted()}
              columnName='NEXT AVAIL.'
            />
          );
        },
        cell: ({ row }) => row.original?.next_available_time_slot?.time_label,
      },
      {
        id: "action",
        cell: ({ row }) => <ReserveButton data={row.original} />,
      },
    ],
    []
  );
};
