"use client";

import { Calendar } from "lucide-react";

import { useStoreValue } from "@/app/StoreContext";

import { ReserveEquipmentButton } from "../../components/reserve-button";
import { EquipmentProps } from "../../types";
import { ActionButton } from "@/components/custom/action-button";
import { CancelButton } from "@/components/custom/cancel-button";
import { useSession } from "@/components/custom/login-auth/auth-provider";

const SessionAvailable = ({ data }: { data: EquipmentProps }) => {
  if (data?.current_user_reservation) {
    return <CancelButton id={Number(data?.current_user_reservation?.id)} />;
  }

  return <ReserveEquipmentButton data={data} />;
};

export const SessionNotAvailable = () => {
  const { dispatch } = useStoreValue(state => state.shouldShowLoginModal);

  return (
    <ActionButton
      onClick={() => dispatch(() => ({ shouldShowLoginModal: true }))}
      icon={<Calendar />}
      className='border-blue-500'
    >
      Login to reserve
    </ActionButton>
  );
};

export const ReserveButton = ({ data }: { data: EquipmentProps }) => {
  const { data: sessionData } = useSession();

  return sessionData ? (
    <SessionAvailable data={data} />
  ) : (
    <SessionNotAvailable />
  );
};
