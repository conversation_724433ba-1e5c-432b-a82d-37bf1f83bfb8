"use client";

import { ColumnDef } from "@tanstack/react-table";

import { useMemo } from "react";

import { LocationColumn } from "./location";
import { EquipmentProps } from "../../types";

export const useMobileColumns = () => {
  return useMemo<ColumnDef<EquipmentProps>[]>(
    () => [
      {
        id: "name",
        header: "Details",
        accessorFn: row => `${row?.name}`,
        cell: ({ row }) => <LocationColumn data={row.original} />,
      },
    ],
    []
  );
};
