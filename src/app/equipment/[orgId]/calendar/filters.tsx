import { useApplyStateToUrl } from "@/common/hooks/useApplyStateToUrl";
import { ALL_OPTION } from "@/components/custom/activity-filters";
import { Button } from "@/components/ui/button";

import { BaseSelect, Option } from "@/components/ui/select";
import { Fragment, useState } from "react";
import { IoFilterSharp } from "react-icons/io5";

export const Filters = ({
  categoriesOptions,
  facilitiesOptions,
  searchParams,
}: {
  searchParams: Record<string, string | number>;
  categoriesOptions: Option[];
  facilitiesOptions: Option[];
}) => {
  const applyToUrl = useApplyStateToUrl();

  const [showFilters, setShowFilters] = useState(false);

  const handleChange = (option: Option, type: string) => {
    if (option.value === ALL_OPTION) {
      return applyToUrl({ [type]: "" });
    }
    return applyToUrl({ [type]: option?.value });
  };

  return (
    <Fragment>
      <div className='flex justify-center'>
        <Button
          className='lg:hidden mb-2 text-center text-sm'
          onClick={() => setShowFilters(!showFilters)}
        >
          <IoFilterSharp className='mr-2' />
          {showFilters ? "Hide Filters" : "Show Filters"}
        </Button>
      </div>
      <div
        className={`flex flex-col lg:flex-row justify-between mb-4 container ${showFilters ? "" : "hidden lg:flex"}`}
      >
        <div className='flex flex-col lg:flex-row gap-3'>
          <BaseSelect
            className='w-full lg:w-[250px]'
            options={facilitiesOptions}
            onChange={val => handleChange(val, "facility_id")}
            placeholder='Choose Location'
            value={String(searchParams?.facility_id) ?? ALL_OPTION}
            name='gym_id'
          />
          <BaseSelect
            className='w-full lg:w-[250px]'
            onChange={val => handleChange(val, "equipment_type_id")}
            placeholder='Select Equipment Type'
            options={categoriesOptions}
            value={String(searchParams?.equipment_type_id) ?? ALL_OPTION}
            name='equipment_type_id'
          />
        </div>
        <div>
          <div className='grid grid-cols-2 lg:grid-cols-4 w-full mt-4 gap-1 lg:justify-end'>
            <div className='flex items-center'>
              <span className='w-4 h-4 bg-red-400 inline-block mr-2'></span>
              <span>Block </span>
            </div>
            <div className='flex items-center'>
              <span className='w-4 h-4 bg-white inline-block mr-2 border border-black md-success-stripes-bg'></span>{" "}
              <span>Available</span>
            </div>
            <div className='flex items-center'>
              <span className='w-4 h-4 bg-[#B3B376] inline-block mr-2'></span>
              <span>Booked</span>
            </div>
            <div className='flex items-center'>
              <span className='w-4 h-4 bg-white inline-block mr-2 border border-black'></span>{" "}
              <span>Unavailable</span>
            </div>
          </div>
        </div>
      </div>
    </Fragment>
  );
};
