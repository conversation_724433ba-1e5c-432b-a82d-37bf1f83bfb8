import { MbscEventcalendarView, MbscResource } from "@mobiscroll/react";
import { EquipmentSlot } from "../types";
import { ClassDetailsResponse } from "@/app/classes/[orgId]/types";

export const defaultCalView = "day";
export const calendarViews: {
  [key: string]: MbscEventcalendarView;
} = {
  month: {
    timeline: {
      type: "month",
      eventList: true,
    },
  },
  week: {
    timeline: {
      type: "week",
      eventList: true,
      weekNumbers: true,
    },
  },
  day: {
    timeline: {
      type: "day",
      timeCellStep: 30,
      timeLabelStep: 30,
    },
  },
  agenda: {
    calendar: { type: "week" },
    agenda: { type: "week" },
  },
};

export const obtainAvailabilityData = (
  slots: EquipmentSlot[],
  equipmentId: number
): MbscResource[] => {
  return slots.map(slot => ({
    cssClass: "md-success-stripes-bg",
    end: slot.end_time,
    id: slot.id,
    recurring: {
      repeat: "daily",
      weekDays: slot?.day_of_week?.substring(0, 2).toUpperCase() as string,
    },
    resource: equipmentId,
    start: slot.start_time,
  }));
};

export const craftReservationEvent = (data: ClassDetailsResponse[]) =>
  data.map(rec => ({
    ...rec,
    color: "#808000",
    end: rec.end_time,
    start: rec.start_time,
    equ_name: `${rec.name}`,
    resource: rec.equipment_id,
    title: `${rec.first_name} ${rec.last_name} - ${rec.equipment_name}`,
    event_type: "pt",
  }));
