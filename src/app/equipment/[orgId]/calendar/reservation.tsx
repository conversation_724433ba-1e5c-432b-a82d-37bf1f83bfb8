import { Dialog, DialogContent } from "@/components/ui/dialog";
import { EquipmentProps, EquipmentType } from "../types";
import { format } from "date-fns";
import { ReserveButton } from "../overview-block/columns/action";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { EquipmentReserveSchema } from "../overview-block/columns/location";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form } from "@/components/ui/form";
import { FormSelect } from "@/components/custom/form-select";

export const Reservation = ({
  isOpen,
  onClose,
  data,
}: {
  isOpen: boolean;
  onClose: () => void;
  data?: EquipmentType;
}) => {
  const form = useForm<z.infer<typeof EquipmentReserveSchema>>({
    resolver: zodResolver(EquipmentReserveSchema),
    defaultValues: {
      time: {
        value: data?.date ? format(data?.date, "HH:mm") : "",
        label: "time",
      },
    },
  });

  return (
    <Form {...form}>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className='lg:w-[400px] text-center'>
          <p className='font-extrabold'>Make a reservation</p>

          <p>
            Date:{" "}
            <span className='font-semibold'>
              {data?.date ? format(data?.date, "MMM d, uuuu") : ""}
            </span>
          </p>

          <p>
            Time:{" "}
            <span className='font-semibold'>
              {data?.date ? format(data?.date, "h:mmaaa") : ""}
            </span>
          </p>

          <div className='my-5 flex flex-col gap-4 z-20 relative'>
            <FormSelect
              className='lg:w-full w-full'
              options={data?.durations || []}
              name='duration'
              placeholder='Duration'
            />
            <FormSelect
              className='lg:w-full w-full'
              placeholder='People attending'
              options={data?.attending_persons || []}
              name='attending_persons'
            />
          </div>

          <div className='flex items-center gap-2'>
            <Button variant={"outline"} onClick={onClose}>
              Cancel
            </Button>
            <ReserveButton data={data as unknown as EquipmentProps} />
          </div>
        </DialogContent>
      </Dialog>
    </Form>
  );
};
