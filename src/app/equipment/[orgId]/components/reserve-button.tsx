"use client";
import { formatDate } from "@/common/common.utils";
import { Calendar } from "lucide-react";
import { cn } from "@/lib/utils";
import { useReserveEquipment } from "../mutations/useReserveEquipment";
import { EquipmentFormType } from "../overview-block/columns/location";
import { useFormContext } from "react-hook-form";
import { EquipmentProps } from "../types";
import { useSearchParams } from "next/navigation";
import { ActionButton } from "@/components/custom/action-button";
import { useStoreValue } from "@/app/StoreContext";

export const ReserveEquipmentButton = ({
  data,
  className,
}: {
  data: EquipmentProps;
  className?: string;
}) => {
  const { handleSubmit, reset } = useFormContext<EquipmentFormType>();

  const { dispatch } = useStoreValue();

  const date = useSearchParams().get("date");

  const { mutate: handleReservation, isPending } = useReserveEquipment(() => {
    reset();
    dispatch(() => ({
      reservation: { ...data, date },
    }));
  });

  const onSubmit = (formData: EquipmentFormType) =>
    handleReservation({
      date: formatDate(date),
      equipment_id: data.id,
      type: "equipment",
      time: formData.time.value,
      duration: formData.duration.value,
      attending_persons: formData.attending_persons.value,
    });

  return (
    <ActionButton
      icon={<Calendar />}
      className={cn("border-blue-500", className)}
      isLoading={isPending}
      variant={"outline"}
      onClick={handleSubmit(onSubmit)}
      type='submit'
    >
      Reserve
    </ActionButton>
  );
};
