import { fetchClientConfigs } from "@/common/api/fetchClientConfigs";
import { SchedulePage } from "./schedule";
import { Footer } from "@/components/custom/footer";

export default async function Schedule({
  params,
  searchParams,
}: Readonly<{
  params: {
    orgId: string;
  };
  searchParams: Record<string, string | number>;
}>) {
  const {
    facilitiesOptions,
    equipmentCategoriesOptions,
    appUrls,
    clientName,
    clientLogo,
    clientPdfHeader,
  } = await fetchClientConfigs(params.orgId);

  return (
    <div className='overflow-auto h-screen'>
      <SchedulePage
        facilitiesOptions={facilitiesOptions}
        categoriesOptions={equipmentCategoriesOptions}
        clientName={clientName}
        clientId={params.orgId}
        clientLogo={clientLogo}
        clientPdfHeader={clientPdfHeader}
        appUrls={appUrls}
        searchParams={searchParams}
      />
      <Footer
        appUrls={appUrls}
        infoText='Make reservations today on the go! Download the app today:'
      />
    </div>
  );
}
