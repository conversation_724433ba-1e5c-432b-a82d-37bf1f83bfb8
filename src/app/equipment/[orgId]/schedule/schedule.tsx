"use client";

import { BaseSelect, Option } from "@/components/ui/select";
import { usePDFFilters } from "@/components/pdf-downloader-module/usePDFFilters";

import { formatDate, parseISO } from "date-fns";
import { ALL_OPTION } from "@/components/custom/activity-filters";
import { ChevronLeft, ChevronRight, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useApplyStateToUrl } from "@/common/hooks/useApplyStateToUrl";
import { useEffect, useRef } from "react";
import { PDFDownloader } from "@/components/pdf-downloader-module/downloader";
import { useEquipmentWeeklyData } from "../queries/useWeeklyEquipmentData";
import { EquipmentPDFCreator } from "@/components/pdf-downloader-module/equipment-pdf-creator";
import { Calendar } from "./calendar";

export const SchedulePage = ({
  categoriesOptions,
  facilitiesOptions,
  clientId,
  clientLogo,
  clientName,
  appUrls,
  searchParams,
  clientPdfHeader,
}: {
  categoriesOptions: Option[];
  facilitiesOptions: Option[];
  clientName?: string;
  clientId?: string;
  clientLogo?: string;
  clientPdfHeader?: string;
  appUrls?: Record<string, string>;
  searchParams: Record<string, string | number>;
}) => {
  const {
    startWeek,
    endOfWeek,
    configs,
    canGoPrev,
    onNextWeek,
    onPreviousWeek,
  } = usePDFFilters({
    initialValues: {
      gym_id: String(facilitiesOptions?.[1]?.value) || "",
      class_category_id: ALL_OPTION,
    },
  });

  const { data, isPending } = useEquipmentWeeklyData(
    clientId as string,
    {
      facility_id:
        searchParams.facility_id === ALL_OPTION
          ? ""
          : searchParams?.facility_id ?? "",
      equipment_type_id: searchParams?.equipment_type_id ?? "",
      start_date: startWeek,
      end_date: endOfWeek,
    },
    true
  );

  const applyToUrl = useApplyStateToUrl();

  const defaultValue = useRef({ facility_id: searchParams?.facility_id });
  const applyToUrlRef = useRef(applyToUrl);
  const facilitiesRef = useRef(facilitiesOptions);

  useEffect(() => {
    if (!defaultValue.current?.facility_id) {
      applyToUrlRef.current({
        facility_id: String(facilitiesRef.current?.[1]?.value) || "",
      });
    }
  }, []);

  const activity = facilitiesOptions.find(
    fac => fac.value === searchParams?.facility_id
  )?.label;

  const category = categoriesOptions.find(
    cat => cat.value === searchParams?.equipment_type_id
  )?.label;

  const handleChange = (option: Option, type: string) => {
    if (option.value === ALL_OPTION) {
      return applyToUrl({ [type]: "" });
    }
    return applyToUrl({ [type]: option?.value, facilities: null });
  };
  return (
    <div className='flex-col'>
      <div className='container my-5 flex flex-col lg:flex-row gap-4 lg:items-center justify-center mx-auto'>
        <BaseSelect
          className='w-full lg:w-[250px]'
          options={facilitiesOptions}
          onChange={val => handleChange(val, "facility_id")}
          placeholder='Choose Activity'
          value={String(searchParams?.facility_id) ?? ALL_OPTION}
          name='gym_id'
        />
        <BaseSelect
          className='w-full lg:w-[250px]'
          onChange={val => handleChange(val, "equipment_type_id")}
          placeholder='Select Equipment Type'
          options={categoriesOptions}
          value={String(searchParams?.equipment_type_id) ?? ALL_OPTION}
          name='equipment_type_id'
        />
        <PDFDownloader
          document={
            <EquipmentPDFCreator
              data={data || []}
              clientName={clientName}
              weekPeriod={`${startWeek} - ${endOfWeek}`}
              activityType={activity}
              configs={configs}
              logo={clientLogo}
              header={clientPdfHeader}
              category={category}
              androidUrl={appUrls?.android}
              iosUrl={appUrls?.ios}
            />
          }
          fileName={`${clientName}-schedule-${startWeek}-${endOfWeek}`}
        >
          <Button className='w-full' disabled={Boolean(!data?.length)}>
            Download Weekly PDF
          </Button>
        </PDFDownloader>
      </div>

      <p className='text-center pt-4 font-bold  mb-4 text-2xl'>
        {"Schedule for Week"}
      </p>
      <div className='flex items-center justify-center w-full'>
        <button
          title='previous week'
          type='button'
          disabled={!canGoPrev}
          onClick={() => onPreviousWeek()}
          style={{ color: configs?.accent_color }}
          className={`transition ease-in-out duration-150 ${!canGoPrev ? "opacity-50 cursor-not-allowed" : "hover:opacity-75"}`}
        >
          <ChevronLeft width={50} height={50} />
        </button>

        <p className='text-center pt-4 font-bold  mb-4 text-2xl'>
          {formatDate(parseISO(startWeek), "MMMM dd")} - {""}
          {formatDate(parseISO(endOfWeek), "MMMM dd, yyyy")}
        </p>
        <button
          style={{ color: configs?.accent_color }}
          title='next week'
          type='button'
          onClick={() => onNextWeek()}
        >
          <ChevronRight width={50} height={50} />
        </button>
      </div>

      {isPending && (
        <div className='flex justify-center'>
          <div className='flex justify-center'>
            <Loader2 className='animate-spin font-bold text-5xl' />
          </div>
        </div>
      )}

      <Calendar
        activity={activity}
        category={category}
        data={data || []}
        color={configs?.accent_color}
        isLoading={isPending}
      />
    </div>
  );
};
