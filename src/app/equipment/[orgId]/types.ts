import { Option } from "@/components/ui/select";

export interface EquipmentProps {
  end_time?: string;
  start_time?: string;
  id: number;
  gym_id: number;
  room_id: number;
  total_slots: number;
  name: string;
  use_room_times: number;
  duration_time_interval: number;
  max_reservations: number;
  min_time_lt: string;
  mac_time_lt: string;
  gym_name: string;
  room_name: string;
  day_of_week: string;
  uniq: string;
  facility_closed: boolean;
  time_slots?: TimeSlotsEntity[] | null;
  slots_available: number;
  available_time_slots?: AvailableTimeSlotsEntity[] | null;
  durations?: Option[] | null;
  attending_persons?: Option[] | null;
  next_available_time_slot?: AvailableTimeSlotsEntity | null;
  status?: string;
  current_user_reservation?: Record<string, string | number>;
  current_status?: string;
}
export interface TimeSlotsEntity {
  equ_slot_id: number;
  start_time: string;
  end_time: string;
  day_of_week: string;
}
export interface AvailableTimeSlotsEntity {
  time_value: string;
  time_label: string;
  reservations_at_slot: number;
  remarks: string;
  allow_reservations: boolean;
}
export interface DurationsEntity {
  value: number;
  label: string;
}

export interface EquipmentWeeklyType {
  schedule: EquipmentSchedule[];
}

export interface EquipmentSchedule {
  equipment: EquipmentType;
  slots: EquipmentSlot[];
}

export interface EquipmentType {
  id: number;
  name: string;
  facility: string;
  attending_persons?: Option[];
  durations?: Option[];
  date?: Date;
  equipment_name?: string;
}

export interface EquipmentSlot {
  id: number;
  start_time: string;
  end_time: string;
  day_of_week?: string;
}
