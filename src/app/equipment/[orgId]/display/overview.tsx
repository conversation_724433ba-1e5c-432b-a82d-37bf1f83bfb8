"use client";

import { useParams } from "next/navigation";

import { DataTable } from "@/components/data-table/data-table";

import { ColumnDef } from "@tanstack/react-table";
import { EquipmentProps } from "../types";
import { useEquipmentData } from "../queries/useEquipmentData";
import { format } from "date-fns";
import Link from "next/link";

const columns = [
  {
    accessorKey: "name",
    header: () => <h1 className='font-semibold text-lg'>Location</h1>,
    cell: ({ row }) => (
      <span className='text-black font-semibold text-lg'>
        {row.original?.name}
      </span>
    ),
  },
  {
    accessorKey: "current_status",
    header: <h1 className='font-semibold text-lg'>Current Status</h1>,
    cell: ({ row }) => (
      <span className='text-black text-lg'>
        {row.original?.current_status ?? "-"}
      </span>
    ),
  },
  {
    accessorFn: row => row?.next_available_time_slot?.time_label,
    id: "next_available_time_slot",
    header: <h1 className='font-semibold text-lg'>Next Available</h1>,
    cell: ({ row }) => (
      <span className='text-black font-semibold text-lg ml-8'>
        {row.original?.next_available_time_slot?.time_label ?? "-"}
      </span>
    ),
  },
] as ColumnDef<EquipmentProps>[];

export const EquipmentOverview = ({
  searchParams,
  scheduleUrl,
}: {
  searchParams: Record<string, string | number>;
  scheduleUrl?: string;
}) => {
  const { orgId } = useParams();
  const { isPending, data } = useEquipmentData(String(orgId), searchParams);

  return (
    <div className='p-6 flex flex-col h-max-content'>
      <div className='mt-8 mb-8'>
        <p className='col-span-1 text-2xl font-semibold pb-4'>
          Schedule for {format(new Date(), "EEE MMMM d")}
        </p>
        {scheduleUrl && (
          <Link href={scheduleUrl} target='__blank'>
            Visit {scheduleUrl} for full schedule
          </Link>
        )}
      </div>

      <DataTable
        classNames='pb-16'
        showFilters={false}
        isLoading={isPending}
        data={data ?? []}
        columns={columns}
      />
    </div>
  );
};
