import { Footer } from "@/components/custom/footer";

import { EquipmentOverview } from "./overview-block/overview";
import { DateFormatPicker } from "@/components/custom/date-picker/date-picker";
import { ActivityFilters } from "@/components/custom/activity-filters";
import { fetchClientConfigs } from "@/common/api/fetchClientConfigs";
import { LoginModal } from "@/components/custom/login-auth/login-modal";
import { ReservationsModal } from "@/components/custom/reservations/reservations-modal";
import { EquipmentDownloadPDF } from "@/components/pdf-downloader-module/equipment-pdf-modal";

export default async function EquipmentsPage({
  params,
  searchParams,
}: Readonly<{
  params: {
    orgId: string;
  };
  searchParams: Record<string, string | number>;
}>) {
  const {
    facilitiesOptions,
    equipmentCategoriesOptions,
    configs,
    appUrls,
    clientLogo,
    clientName,
    clientPdfHeader,
  } = await fetchClientConfigs(params?.orgId);
  return (
    <div className='h-screen'>
      <DateFormatPicker />
      <ActivityFilters
        facilitiesOptions={facilitiesOptions}
        categoriesOptions={equipmentCategoriesOptions}
        activityName='equipment_type_id'
      />
      <LoginModal
        buttonColor={configs?.embed?.button_color}
        appUrls={appUrls}
        configs={configs}
        orgId={params.orgId}
      />
      <EquipmentOverview searchParams={searchParams} />
      <ReservationsModal />
      <EquipmentDownloadPDF
        facilitiesOptions={facilitiesOptions}
        categoriesOptions={equipmentCategoriesOptions}
        clientName={clientName}
        clientId={params.orgId}
        clientLogo={clientLogo}
        clientPdfHeader={clientPdfHeader}
        appUrls={appUrls}
      />
      <Footer
        appUrls={appUrls}
        infoText='Make reservations today on the go! Download the app today:'
      />
    </div>
  );
}
