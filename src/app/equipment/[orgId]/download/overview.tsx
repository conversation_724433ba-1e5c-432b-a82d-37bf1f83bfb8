"use client";

import { useEquipmentWeeklyData } from "@/app/equipment/[orgId]/queries/useWeeklyEquipmentData";
import { formatDate } from "@/common/common.utils";
import { ALL_OPTION } from "@/components/custom/activity-filters";
import { PDFDownloader } from "@/components/pdf-downloader-module/downloader";
import { EquipmentPDFCreator } from "@/components/pdf-downloader-module/equipment-pdf-creator";

import { usePDFFilters } from "@/components/pdf-downloader-module/usePDFFilters";
import { Button } from "@/components/ui/button";
import { BaseSelect, Option } from "@/components/ui/select";
import { lastDayOfWeek, startOfWeek } from "date-fns";
import { DownloadCloud, Loader2, X } from "lucide-react";
import { Fragment } from "react";

export const EquipmentPDFOverview = ({
  categoriesOptions = [],
  facilitiesOptions = [],
  clientName,
  clientId,
  clientLogo,
  clientPdfHeader,
  appUrls,
}: {
  categoriesOptions: Option[];
  facilitiesOptions: Option[];
  clientName?: string;
  clientId?: string;
  clientLogo?: string;
  clientPdfHeader?: string;
  appUrls?: Record<string, string>;
}) => {
  const {
    field,
    handleChange,
    handleClose,
    formatEndOfWeek,
    formatStartWeek,
    enableRequest,
    configs,
  } = usePDFFilters();

  const {
    data,
    isFetching,
    isPending: isLoading,
  } = useEquipmentWeeklyData(
    clientId as string,
    {
      facility_id: field.gym_id === ALL_OPTION ? "" : field.gym_id,
      equipment_type_id:
        field.class_category_id === ALL_OPTION ? "" : field.class_category_id,
      start_date: formatDate(startOfWeek(new Date())),
      end_date: formatDate(lastDayOfWeek(new Date())),
    },
    enableRequest // enable request only when modal is open
  );

  const activity = facilitiesOptions.find(
    fac => fac.value === field.gym_id
  )?.label;

  const category = categoriesOptions.find(
    cat => cat.value === field.class_category_id
  )?.label;

  return (
    <Fragment>
      <div className='text-center'>
        <p className='text-center'>Download weekly schedule</p>
        <p className='text-center pt-4 font-bold'>
          {formatStartWeek} - {formatEndOfWeek}
        </p>

        {!isLoading && data?.length === 0 ? (
          <div className='text-red-500 text-center'>
            <p>There is no data to download</p>
            <p> Please select a different location</p>
          </div>
        ) : (
          <p className='text-center'>Please select location to download</p>
        )}
      </div>
      <div className='my-5 flex flex-col gap-4'>
        <BaseSelect
          className='w-full'
          onChange={handleChange}
          placeholder='Select Location'
          options={facilitiesOptions}
          value={field?.gym_id}
          name='gym_id'
          menuPlacement='bottom'
        />
        <BaseSelect
          className='w-full'
          onChange={handleChange}
          placeholder={"Select Equipment Type"}
          options={categoriesOptions}
          value={field?.class_category_id}
          name='class_category_id'
          menuPlacement='bottom'
        />
      </div>
      <div className='flex justify-between'>
        <Button onClick={() => handleClose()} variant={"outline"}>
          <X className='mr-2 w-4 hover:bg-white' color='blue' />
          <span>Cancel</span>
        </Button>
        <Button
          disabled={isFetching || !data?.length}
          className='text-white w-3/5'
        >
          <PDFDownloader
            document={
              <EquipmentPDFCreator
                data={data || []}
                clientName={clientName}
                weekPeriod={`${formatStartWeek} - ${formatEndOfWeek}`}
                activityType={activity}
                configs={configs}
                logo={clientLogo}
                header={clientPdfHeader}
                category={category}
                androidUrl={appUrls?.android}
                iosUrl={appUrls?.ios}
              />
            }
            fileName={`${clientName}-schedule-${formatStartWeek}-${formatEndOfWeek}`}
            className='flex items-center  gap-2'
          >
            {isFetching ? (
              <Loader2 className='mr-2 h-4 w-4 animate-spin text-blue-500' />
            ) : (
              <DownloadCloud
                className='mr-2 h-4 w-4 hover:bg-blue-600'
                color='white'
              />
            )}

            {isFetching ? (
              <span>Loading...</span>
            ) : (
              <span>Download schedule</span>
            )}
          </PDFDownloader>
        </Button>
      </div>
    </Fragment>
  );
};
