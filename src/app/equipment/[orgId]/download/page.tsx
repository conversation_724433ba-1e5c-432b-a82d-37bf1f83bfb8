import { fetchClientConfigs } from "@/common/api/fetchClientConfigs";
import { EquipmentPDFOverview } from "./overview";

export default async function EquipmentDownloadPage({
  params,
}: Readonly<{
  params: {
    orgId: string;
  };
  searchParams: Record<string, string | number>;
}>) {
  const {
    facilitiesOptions,
    appUrls,
    clientName,
    clientLogo,
    clientPdfHeader,
    equipmentCategoriesOptions,
  } = await fetchClientConfigs(params.orgId);

  return (
    <EquipmentPDFOverview
      facilitiesOptions={facilitiesOptions}
      categoriesOptions={equipmentCategoriesOptions}
      clientName={clientName}
      clientId={params.orgId}
      clientLogo={clientLogo}
      clientPdfHeader={clientPdfHeader}
      appUrls={appUrls}
    />
  );
}
