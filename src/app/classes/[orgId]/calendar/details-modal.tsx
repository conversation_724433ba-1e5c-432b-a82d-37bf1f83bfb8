"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";

import { Users, DoorClosed, X } from "lucide-react";
import { ClassDetailsResponse } from "../types";
import { ImageCard } from "../modules/overview-block/details/details-image";
import { InfoDetailsRow } from "@/components/custom/info-details-rows";
import { IoLocationOutline } from "react-icons/io5";
import { Instructors } from "../modules/overview-block/details/instructor";
import { LoginAction } from "../modules/overview-block/columns/action-cell";
import { Fragment } from "react";
import { UpcomingClasses } from "./upcoming-class";
import { LoginToReserve } from "./login-reserve";
import { useSession } from "@/components/custom/login-auth/auth-provider";

export const ClassModal = ({
  classDetails,
  setIsOpen,
  isOpen,
  orgId,
}: {
  classDetails: ClassDetailsResponse;
  isOpen: boolean;
  setIsOpen: (value?: boolean) => void;
  orgId: string;
}) => {
  const { data: sessionData } = useSession();

  return (
    <Fragment>
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className='w-full h-[90%] md:h-[85%] lg:h-[80%] md:max-w-[450px] lg:max-w-[480px] md:rounded-lg border-white overflow-y-auto overflow-x-hidden outline-none p-0 md:p-0'>
          <div className='sticky top-0 left-0 right-0 z-50 flex justify-between items-center bg-white/90 backdrop-blur-sm p-2 sm:p-3 md:p-4 shadow-sm md:border-b md:border-gray-200'>
            <DialogTitle className='text-base sm:text-lg md:text-xl font-semibold text-[#009DC4]'>
              Class Details
            </DialogTitle>
            <Button
              onClick={() => setIsOpen(false)}
              size='sm'
              variant='outline'
              className='rounded-md h-8 md:h-9 border-[#009DC4] text-[#009DC4] hover:bg-[#009DC4]/10 font-medium'
            >
              <X className='h-4 w-4 md:h-5 md:w-5 mr-1' />
              <span className='text-xs sm:text-sm md:text-base'>Close</span>
            </Button>
          </div>
          <div className='grid h-full gap-3 sm:gap-4 md:gap-4 pt-0 pb-24 md:pb-28 px-3 sm:px-4 md:px-5 overflow-y-auto overflow-x-hidden'>
            <ImageCard
              imageSrc={classDetails?.images?.[0] as string}
              startDay={classDetails?.selected_date as string}
              startTime={classDetails?.start_time}
              endTime={classDetails?.end_time}
            />

            <div className='mt-2 sm:mt-3 md:mt-3'>
              <p className='text-[#009DC4] font-bold text-lg md:text-lg mb-2 md:mb-2'>
                {classDetails.name}
              </p>
              <div className='space-y-2 sm:space-y-3 md:space-y-3'>
                {classDetails.room_name && (
                  <InfoDetailsRow
                    icon={<DoorClosed className='md:h-5 md:w-5' />}
                  >
                    {classDetails?.room_name}
                  </InfoDetailsRow>
                )}
                {classDetails?.category && (
                  <InfoDetailsRow icon={<Users className='md:h-5 md:w-5' />}>
                    {classDetails?.category}
                  </InfoDetailsRow>
                )}
                <InfoDetailsRow
                  icon={<IoLocationOutline className='w-5 h-5 md:w-5 md:h-5' />}
                >
                  {classDetails.is_virtual ? "Virtual Class" : "Live Class"}
                </InfoDetailsRow>
              </div>
            </div>

            <div className='mt-2 sm:mt-3 md:mt-4'>
              <Instructors
                label=''
                primaryInstructor={classDetails?.instructor}
                subInstructor={classDetails?.subbing_instructor}
                className='mb-0 pt-0 pb-0 pl-0'
              />
              <div className='border-t border-gray-300 mt-2 sm:mt-3 md:mt-4' />
            </div>

            {classDetails?.description && (
              <div className='px-2 py-1 sm:p-4 md:p-4'>
                <p className='text-gray-600 text-sm sm:text-base md:text-base'>
                  {classDetails?.description}
                </p>
              </div>
            )}
            <UpcomingClasses data={classDetails} />
          </div>

          {!sessionData && (
            <LoginToReserve
              spotsAvailable={classDetails?.day_record?.spots_available}
              orgId={orgId}
              data={classDetails.day_record as ClassDetailsResponse}
            />
          )}

          {sessionData && (
            <DialogFooter className='fixed bottom-0 bg-gray-100 w-full py-3 sm:py-4 md:py-4 grid grid-cols-2 items-center z-50 border-t border-gray-200'>
              <p className='pl-3 md:pl-5 text-sm sm:text-base md:text-base font-semibold items-center'>{`${classDetails?.day_record?.spots_available} spot left`}</p>
              {
                <div className='pr-3 sm:pr-4 md:pr-5'>
                  <LoginAction
                    data={
                      classDetails?.day_record as unknown as ClassDetailsResponse
                    }
                  />
                </div>
              }
            </DialogFooter>
          )}
        </DialogContent>
      </Dialog>
    </Fragment>
  );
};
