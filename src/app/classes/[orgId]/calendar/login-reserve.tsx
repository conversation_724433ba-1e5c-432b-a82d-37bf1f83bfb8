"use client";
import {
  <PERSON>ginForm,
  LoginSchema,
} from "@/components/custom/login-auth/login-form";
import { useState } from "react";
import { z } from "zod";
import { useReserveMutation } from "../mutations/useReserveClass";
import { useSession } from "@/components/custom/login-auth/auth-provider";
import { useStoreValue } from "@/app/StoreContext";
import { ClassDetailsResponse } from "../types";
import { ActionButton } from "@/components/custom/action-button";

export const LoginToReserve = ({
  spotsAvailable,
  orgId,
  data,
}: {
  spotsAvailable?: number;
  orgId?: string;
  data: ClassDetailsResponse;
}) => {
  const [isLoading, setIsLoading] = useState(false);

  const { signIn, isError } = useSession();

  const { dispatch } = useStoreValue();

  const {
    mutate: handleReservation,
    isSuccess: isReserveSuccess,
    isPending,
  } = useReserveMutation(() =>
    dispatch(() => ({
      reservation: { ...data },
    }))
  );

  const handleSubmit = async (fieldValue: z.infer<typeof LoginSchema>) => {
    try {
      setIsLoading(true);
      await signIn({
        email: fieldValue.email,
        password: fieldValue.password,
        orgId,
      });

      handleReservation({
        date: data.date,
        class_id: data.id,
        type: "class",
      });
    } catch (error) {
      throw new Error("Something bad happened, try again");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className='container'>
      {data.is_past ? (
        <ActionButton style={{ background: "grey", color: "black" }} isDisabled>
          Past Class – Reservation Closed
        </ActionButton>
      ) : (
        <>
          <p className='flex justify-center bg-gray-400 rounded-sm font-bold p-2 text-white'>{`${spotsAvailable} spot left`}</p>
          {!isReserveSuccess && (
            <LoginForm
              handleSubmit={handleSubmit}
              isError={isError}
              placeholder='Login to Reserve'
              isLoading={isPending || isLoading}
              className='lg:w-full'
            />
          )}
        </>
      )}
    </div>
  );
};
