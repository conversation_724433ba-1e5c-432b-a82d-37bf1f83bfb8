import { isAfter } from "date-fns";
import { formatTime } from "../classes.utils";
import { LoginAction } from "../modules/overview-block/columns/action-cell";
import { ClassDetailsResponse } from "../types";
import { format } from "date-fns";

export const UpcomingClasses = ({ data }: { data: ClassDetailsResponse }) => {
  const futureDates = data?.date_info
    ?.filter(
      info =>
        isAfter(new Date(info.date), new Date(data.day_record?.date ?? "")) &&
        !info.is_past
    )
    .slice(0, 3);

  return futureDates?.length ? (
    <div className='mb-20 sm:mb-24 md:mb-24 px-2 sm:px-0 md:px-0'>
      <h2 className='text-base sm:text-lg md:text-lg font-semibold mb-2 md:mb-3'>
        Upcoming Classes:
      </h2>

      {futureDates?.map(ftDate => (
        <div
          className='p-4 rounded-md border-2 mt-3 sm:mt-4 md:mt-4 gap-2 mb-2 md:mb-3 overflow-hidden'
          key={ftDate.dow}
        >
          <div className='grid grid-cols-1 sm:grid-cols-2 gap-3'>
            <div>
              <p className='text-sm sm:text-base md:text-base font-medium truncate'>
                {data.name}
              </p>
              <p className='text-xs sm:text-sm md:text-sm mt-1 md:mt-1'>
                {format(ftDate.date, "EEEE, MMMM d")}
              </p>
              <p className='text-xs sm:text-sm md:text-sm mt-1 md:mt-1'>{`${formatTime(data.start_time)}-${formatTime(data.end_time)}`}</p>
            </div>

            {!ftDate.is_past && (
              <div className='flex items-center justify-start sm:justify-end'>
                <div className='w-[100px] sm:w-auto'>
                  <LoginAction
                    data={
                      {
                        ...data,
                        ...ftDate,
                      } as ClassDetailsResponse
                    }
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      ))}
    </div>
  ) : null;
};
