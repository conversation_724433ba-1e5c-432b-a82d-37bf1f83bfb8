"use client";

import { format, addDays } from "date-fns";
import { Plus } from "lucide-react";

import { obtainDateFrame } from "../classes.utils";
import { styled } from "styled-components";
import tw from "twin.macro";
import { useStoreValue } from "@/app/StoreContext";
import { ClassDateInfo, ClassDetailsResponse } from "../types";
import { formatDate } from "@/common/common.utils";
import {
  Badge,
  Recommended,
} from "../modules/overview-block/columns/helper-components";
import Link from "next/link";

const daysOfWeek = [
  { value: "mon", label: "MONDAY" },
  { value: "tue", label: "TUESDAY" },
  { value: "wed", label: "WEDNESDAY" },
  { value: "thu", label: "THURSDAY" },
  { value: "fri", label: "FRIDAY" },
  { value: "sat", label: "SATURDAY" },
  { value: "sun", label: "SUNDAY" },
] as const;

const StyledHeader = styled.div<{ color?: string }>`
  ${tw`font-bold p-2 text-white`};
  background-color: ${({ color }) => color};
`;

interface ClassCardProps {
  cls: ClassDetailsResponse;
  date: Date;
  selectedDate?: ClassDetailsResponse & ClassDateInfo;
  day_record?: ClassDetailsResponse;
  color?: string;
  setSelected?: React.Dispatch<
    React.SetStateAction<ClassDetailsResponse | undefined>
  >;
  selected?: ClassDetailsResponse | undefined;
}

const ClassCard: React.FC<ClassCardProps> = ({
  cls,
  date,
  selectedDate,
  day_record,
  color,
  setSelected,
}) => {
  return (
    <Link
      href={""}
      onClick={() =>
        setSelected?.({
          ...cls,
          selected_date: formatDate(date),
          //@ts-expect-error @todo: to update types
          day_record,
        })
      }
      className={`bg-white border rounded-sm cursor-pointer border-gray-200 shadow-sm h-[165px] gap-0 flex flex-col  ${selectedDate ? "hover:bg-gray-100" : ""}`}
    >
      <div className={`${selectedDate?.cancelled ? "line-through" : ""}`}>
        <div className='text-sm p-2 pb-0 flex justify-between items-center'>
          <span>{obtainDateFrame(cls.start_time, cls.end_time)}</span>
          <Plus
            onClick={() =>
              setSelected?.({
                ...cls,
                selected_date: formatDate(date),
                day_record: Object.assign(
                  { id: cls.id, date: formatDate(date) },
                  selectedDate
                ),
              })
            }
            style={{ color }}
            className='h-4 w-4 cursor-pointer'
          />
        </div>
        <div className='p-2 pt-0  flex-grow flex flex-col justify-between'>
          <div className='font-bold truncate max-w-56'>{cls.name}</div>
          <div className='flex'>
            <p className='block text-sm truncate max-w-56 font-medium text-gray-600'>
              {selectedDate?.is_class_subbed
                ? selectedDate?.subbing_instructor
                : cls.instructor}
            </p>

            {selectedDate?.is_class_subbed && (
              <p className='text-red-500 font-bold text-xs pl-1'>(SUB)</p>
            )}
          </div>
          <p className='text-sm truncate max-w-56 text-gray-600'>
            {cls.room_name}
          </p>
          <p className='text-sm truncate max-w-56 text-gray-600'>
            {cls?.gym_name}
          </p>
        </div>
      </div>
      <div className='p-2'>
        {selectedDate?.is_recommended && <Recommended className='mt-0 mb-1' />}
        <section className='mt-0 pb-6'>
          <Badge data={day_record as ClassDetailsResponse} />
        </section>
      </div>
    </Link>
  );
};

interface DayColumnProps {
  day: (typeof daysOfWeek)[number];
  date: Date;
  dayClasses: ClassDetailsResponse[] | undefined;
  color?: string;
  setSelected: React.Dispatch<
    React.SetStateAction<ClassDetailsResponse | undefined>
  >;
  selected: ClassDetailsResponse | undefined;
}

const DayColumn: React.FC<DayColumnProps> = ({
  day,
  date,
  dayClasses,
  color,
  setSelected,
  selected,
}) => {
  return (
    <div className='border overflow-hidden bg-white shadow cursor-pointer hover:bg-gray-100'>
      <StyledHeader color={color} className='flex justify-between'>
        <span>{day.label}</span>
        <span>{format(date, "d")}</span>
      </StyledHeader>
      <div className='p-2 space-y-2'>
        {dayClasses?.map((cls, i) => {
          const selectedDate = cls.date_info?.find(
            ({ dow }) => dow === day.value
          );

          const day_record = Object.assign(
            {
              ...cls,
              date: formatDate(date),
            },
            selectedDate
          );

          return (
            <div key={i}>
              <ClassCard
                cls={cls}
                date={date}
                selectedDate={
                  selectedDate as ClassDetailsResponse & ClassDateInfo
                }
                day_record={day_record}
                color={color}
                setSelected={setSelected}
                selected={selected}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
};

interface ClassCalendarProps {
  data: ClassDetailsResponse[] | undefined;
  weekStart: Date;
  setSelected: React.Dispatch<
    React.SetStateAction<ClassDetailsResponse | undefined>
  >;
  selected: ClassDetailsResponse | undefined;
}

export const ClassCalendar = ({
  weekStart,
  data,
  selected,
  setSelected,
}: ClassCalendarProps) => {
  const { state: configs } = useStoreValue(store => store.configs);

  return (
    <div className='relative z-0'>
      <div className='grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-7 gap-2 z-0'>
        {daysOfWeek.map((day, index) => {
          const date = addDays(weekStart, index);
          const dayClasses = data?.filter(c =>
            c.days_of_week.includes(day.value)
          );

          return (
            <div key={day?.value}>
              <DayColumn
                day={day}
                date={date}
                dayClasses={dayClasses}
                color={configs?.accent_color}
                setSelected={setSelected}
                selected={selected}
              />
            </div>
          );
        })}
      </div>
      {!data?.length && (
        <p className='text-center pt-4 font-semibold'>
          Sorry there are no classes available, Please update your filters
        </p>
      )}
    </div>
  );
};
