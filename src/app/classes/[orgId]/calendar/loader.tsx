const Skeleton = ({ className }: { className: string }) => (
  <div aria-live='polite' aria-busy='true' className={className}>
    <span className='inline-flex w-full animate-pulse select-none rounded-md bg-gray-300 leading-none'>
      ‌
    </span>
    <br />
  </div>
);

const SVGSkeleton = ({ className }: { className: string }) => (
  <svg className={className + " animate-pulse rounded bg-gray-300"} />
);

export const Loader = () => (
  <>
    <div className='grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-7 gap-2'>
      <div className='border'>
        <div className='p-2'>
          <span>
            <Skeleton className='w-[72px] max-w-full' />
          </span>
        </div>
        <div className='p-2 space-y-2'>
          <div className='border border-gray-200 shadow-sm h-[170px] flex flex-col'>
            <div className='p-2 flex justify-between items-center'>
              <span>
                <Skeleton className='w-[128px] max-w-full' />
              </span>
              <SVGSkeleton className='w-[24px] h-[24px]' />
            </div>
            <div className='p-2 flex-grow flex flex-col justify-between'>
              <div>
                <div>
                  <Skeleton className='w-[144px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[56px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[96px] max-w-full' />
                </div>
              </div>
            </div>
          </div>
          <div className='border border-gray-200 shadow-sm h-[170px] flex flex-col'>
            <div className='p-2 flex justify-between items-center'>
              <span>
                <Skeleton className='w-[120px] max-w-full' />
              </span>
              <SVGSkeleton className='w-[24px] h-[24px]' />
            </div>
            <div className='p-2 flex-grow flex flex-col justify-between'>
              <div>
                <div>
                  <Skeleton className='w-[144px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[80px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[168px] max-w-full' />
                </div>
              </div>
              <div className='mt-1'></div>
            </div>
          </div>
        </div>
      </div>
      <div className='border'>
        <div className='p-2'>
          <span>
            <Skeleton className='w-[80px] max-w-full' />
          </span>
        </div>
        <div className='p-2 space-y-2'>
          <div className='border border-gray-200 shadow-sm h-[170px] flex flex-col'>
            <div className='p-2 flex justify-between items-center'>
              <span>
                <Skeleton className='w-[120px] max-w-full' />
              </span>
              <SVGSkeleton className='w-[24px] h-[24px]' />
            </div>
            <div className='p-2 flex-grow flex flex-col justify-between'>
              <div>
                <div>
                  <Skeleton className='w-[144px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[80px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[168px] max-w-full' />
                </div>
              </div>
              <div className='mt-1'></div>
            </div>
          </div>
          <div className='border border-gray-200 shadow-sm h-[170px] flex flex-col'>
            <div className='p-2 flex justify-between items-center'>
              <span>
                <Skeleton className='w-[120px] max-w-full' />
              </span>
              <SVGSkeleton className='w-[24px] h-[24px]' />
            </div>
            <div className='p-2 flex-grow flex flex-col justify-between'>
              <div>
                <div>
                  <Skeleton className='w-[144px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[80px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[168px] max-w-full' />
                </div>
              </div>
              <div className='mt-1'></div>
            </div>
          </div>
        </div>
      </div>
      <div className='border'>
        <div className='p-2'>
          <span>
            <Skeleton className='w-[96px] max-w-full' />
          </span>
        </div>
        <div className='p-2 space-y-2'>
          <div className='border border-gray-200 shadow-sm h-[170px] flex flex-col'>
            <div className='p-2 flex justify-between items-center'>
              <span>
                <Skeleton className='w-[120px] max-w-full' />
              </span>
              <SVGSkeleton className='w-[24px] h-[24px]' />
            </div>
            <div className='p-2 flex-grow flex flex-col justify-between'>
              <div>
                <div>
                  <Skeleton className='w-[136px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[48px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[88px] max-w-full' />
                </div>
              </div>
              <div className='mt-1'></div>
            </div>
          </div>
          <div className='border border-gray-200 shadow-sm h-[170px] flex flex-col'>
            <div className='p-2 flex justify-between items-center'>
              <span>
                <Skeleton className='w-[120px] max-w-full' />
              </span>
              <SVGSkeleton className='w-[24px] h-[24px]' />
            </div>
            <div className='p-2 flex-grow flex flex-col justify-between'>
              <div>
                <div>
                  <Skeleton className='w-[144px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[80px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[168px] max-w-full' />
                </div>
              </div>
              <div className='mt-1'></div>
            </div>
          </div>
        </div>
      </div>
      <div className='border'>
        <div className='p-2'>
          <span>
            <Skeleton className='w-[88px] max-w-full' />
          </span>
        </div>
        <div className='p-2 space-y-2'>
          <div className='border border-gray-200 shadow-sm h-[170px] flex flex-col'>
            <div className='p-2 flex justify-between items-center'>
              <span>
                <Skeleton className='w-[120px] max-w-full' />
              </span>
              <SVGSkeleton className='w-[24px] h-[24px]' />
            </div>
            <div className='p-2 flex-grow flex flex-col justify-between'>
              <div>
                <div>
                  <Skeleton className='w-[144px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[80px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[168px] max-w-full' />
                </div>
              </div>
              <div className='mt-1'></div>
            </div>
          </div>
          <div className='border border-gray-200 shadow-sm h-[170px] flex flex-col'>
            <div className='p-2 flex justify-between items-center'>
              <span>
                <Skeleton className='w-[120px] max-w-full' />
              </span>
              <SVGSkeleton className='w-[24px] h-[24px]' />
            </div>
            <div className='p-2 flex-grow flex flex-col justify-between'>
              <div>
                <div>
                  <Skeleton className='w-[144px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[80px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[168px] max-w-full' />
                </div>
              </div>
              <div className='mt-1'></div>
            </div>
          </div>
        </div>
      </div>
      <div className='border'>
        <div className='p-2'>
          <span>
            <Skeleton className='w-[72px] max-w-full' />
          </span>
        </div>
        <div className='p-2 space-y-2'>
          <div className='border border-gray-200 shadow-sm h-[170px] flex flex-col'>
            <div className='p-2 flex justify-between items-center'>
              <span>
                <Skeleton className='w-[120px] max-w-full' />
              </span>
              <SVGSkeleton className='w-[24px] h-[24px]' />
            </div>
            <div className='p-2 flex-grow flex flex-col justify-between'>
              <div>
                <div>
                  <Skeleton className='w-[152px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[56px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[120px] max-w-full' />
                </div>
              </div>
              <div className='mt-1'>
                <div className='inline-flex items-center border px-2.5 py-0.5 transition-colors border-transparent'>
                  <Skeleton className='w-[88px] max-w-full' />
                </div>
              </div>
            </div>
          </div>
          <div className='border border-gray-200 shadow-sm h-[170px] flex flex-col'>
            <div className='p-2 flex justify-between items-center'>
              <span>
                <Skeleton className='w-[120px] max-w-full' />
              </span>
              <SVGSkeleton className='w-[24px] h-[24px]' />
            </div>
            <div className='p-2 flex-grow flex flex-col justify-between'>
              <div>
                <div>
                  <Skeleton className='w-[144px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[80px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[168px] max-w-full' />
                </div>
              </div>
              <div className='mt-1'></div>
            </div>
          </div>
        </div>
      </div>
      <div className='border'>
        <div className='p-2'>
          <span>
            <Skeleton className='w-[88px] max-w-full' />
          </span>
        </div>
        <div className='p-2 space-y-2'>
          <div className='border border-gray-200 shadow-sm h-[170px] flex flex-col'>
            <div className='p-2 flex justify-between items-center'>
              <span>
                <Skeleton className='w-[120px] max-w-full' />
              </span>
              <SVGSkeleton className='w-[24px] h-[24px]' />
            </div>
            <div className='p-2 flex-grow flex flex-col justify-between'>
              <div>
                <div>
                  <Skeleton className='w-[136px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[48px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[88px] max-w-full' />
                </div>
              </div>
              <div className='mt-1'></div>
            </div>
          </div>
          <div className='border border-gray-200 shadow-sm h-[170px] flex flex-col'>
            <div className='p-2 flex justify-between items-center'>
              <span>
                <Skeleton className='w-[120px] max-w-full' />
              </span>
              <SVGSkeleton className='w-[24px] h-[24px]' />
            </div>
            <div className='p-2 flex-grow flex flex-col justify-between'>
              <div>
                <div>
                  <Skeleton className='w-[144px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[80px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[168px] max-w-full' />
                </div>
              </div>
              <div className='mt-1'></div>
            </div>
          </div>
        </div>
      </div>
      <div className='border'>
        <div className='p-2'>
          <span>
            <Skeleton className='w-[72px] max-w-full' />
          </span>
        </div>
        <div className='p-2 space-y-2'>
          <div className='border border-gray-200 shadow-sm h-[170px] flex flex-col'>
            <div className='p-2 flex justify-between items-center'>
              <span>
                <Skeleton className='w-[120px] max-w-full' />
              </span>
              <SVGSkeleton className='w-[24px] h-[24px]' />
            </div>
            <div className='p-2 flex-grow flex flex-col justify-between'>
              <div>
                <div>
                  <Skeleton className='w-[144px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[80px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[168px] max-w-full' />
                </div>
              </div>
              <div className='mt-1'></div>
            </div>
          </div>
          <div className='border border-gray-200 shadow-sm h-[170px] flex flex-col'>
            <div className='p-2 flex justify-between items-center'>
              <span>
                <Skeleton className='w-[120px] max-w-full' />
              </span>
              <SVGSkeleton className='w-[24px] h-[24px]' />
            </div>
            <div className='p-2 flex-grow flex flex-col justify-between'>
              <div>
                <div>
                  <Skeleton className='w-[144px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[80px] max-w-full' />
                </div>
                <div>
                  <Skeleton className='w-[168px] max-w-full' />
                </div>
              </div>
              <div className='mt-1'></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </>
);
