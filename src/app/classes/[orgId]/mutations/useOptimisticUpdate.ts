import { useQueryClient } from "@tanstack/react-query";
import { useParams } from "next/navigation";
import { classesQueryOptions } from "../queries/useClassesData";
import { getUrlParameters } from "@/lib/url";
import {
  GET_USER_RESERVATIONS,
  GET_USER_WAITLIST,
} from "../queries/useUserReservations";
import { useSession } from "@/components/custom/login-auth/auth-provider";

export function useOptimisticUpdate() {
  const params = useParams();

  const searchParams = getUrlParameters();

  const queryClient = useQueryClient();

  const { data: sessionData } = useSession();

  const queryKey = classesQueryOptions(
    params?.orgId as string,
    searchParams,
    sessionData?.token
  ).queryKey;

  return async () => {
    await queryClient.invalidateQueries({
      queryKey: [GET_USER_WAITLIST],
    });
    await queryClient.invalidateQueries({
      queryKey: [GET_USER_RESERVATIONS],
    });
    return await queryClient.invalidateQueries({
      queryKey,
    });
  };
}
