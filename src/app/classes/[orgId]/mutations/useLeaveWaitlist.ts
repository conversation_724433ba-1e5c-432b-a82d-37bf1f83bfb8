"use client";

import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { leaveWait } from "../actions/leave-waitlist";
import { useOptimisticUpdate } from "./useOptimisticUpdate";

export const useLeaveWaitlist = (onSuccess?: () => void) => {
  const invalidateQueries = useOptimisticUpdate();

  return useMutation({
    mutationFn: leaveWait,

    onSuccess: async data => {
      await invalidateQueries();

      if (data.success) {
        onSuccess?.();
        return toast.success(data?.message);
      }

      return toast.error(data?.message);
    },

    onError: e => {
      return toast.error(e?.message);
    },
  });
};
