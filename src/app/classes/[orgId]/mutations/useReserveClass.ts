"use client";

import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { useOptimisticUpdate } from "./useOptimisticUpdate";
import { reserveAction } from "@/app/actions/reserve";

export const useReserveMutation = (onSuccess?: () => void) => {
  const invalidateClassesQueries = useOptimisticUpdate();

  return useMutation({
    mutationFn: reserveAction,
    onSuccess: async data => {
      await invalidateClassesQueries();

      if (data.success) {
        if (data?.data?.reservation_id) {
          onSuccess?.();
        }
        return toast.success(data?.message);
      }

      return toast.error(data?.message);
    },

    onError: e => toast.error(e?.message),
  });
};
