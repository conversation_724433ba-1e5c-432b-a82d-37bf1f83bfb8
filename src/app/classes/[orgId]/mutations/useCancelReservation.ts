"use client";

import { cancelReservation } from "@/app/classes/[orgId]/actions/cancelReservation";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";
import { useOptimisticUpdate } from "./useOptimisticUpdate";

export const useCancelReservation = (onSuccess?: () => void) => {
  const invalidateQueries = useOptimisticUpdate();

  return useMutation({
    mutationFn: cancelReservation,

    onSuccess: async data => {
      await invalidateQueries();
      if (data.success) {
        onSuccess?.();
        return toast.success(data?.message);
      }

      return toast.error(data?.message);
    },

    onError: e => {
      return toast.error(e?.message);
    },
  });
};
