export interface ClassDetailsResponse {
  id: number;
  gym_id: number;
  room_id: number;
  start_time: string;
  end_time: string;
  name: string;
  description: string;
  allow_reservation_date: string;
  advance_time: number;
  spots: number;
  walkin_spots: number;
  is_sgt: number;
  class_category_id: number;
  tags: string[];
  days_of_week: string[];
  allow_waitlist: number;
  waitlist_spots: number;
  gym_name: string;
  room_name: string;
  slot_id: number;
  category: string;
  instructor_id: number;
  instructor_first_name: string;
  instructor_last_name: string;
  facility_closed: boolean;
  cancelled: boolean;
  allow_reservations: boolean;
  instructor: string;
  is_class_subbed: boolean;
  reservation_count: number;
  waitlist_count: number;
  spots_available: number;
  waitlist_spots_available: number;
  occupancy_total: number;
  images: string[];
  facility_closed_reason?: string;
  cancel_reason?: string;
  subbing_instructor?: string;
  is_recommended?: boolean;
  is_new?: boolean;
  is_full?: boolean;
  is_started?: boolean;
  is_waitlist_available?: boolean;
  start_day?: string;
  date_time?: string;
  current_user_reservation?: Record<string, string | number>;
  is_virtual?: boolean;
  current_user_waitlist?: Record<string, string | number>;
  position_index?: number;
  class_id?: number;
  class_name?: string;
  equipment_name?: string;
  date_info?: ClassDateInfo[];
  equipment_id?: number;
  first_name?: string;
  last_name?: string;
  attending_persons?: number;
  start?: string;
  end?: string;
  title?: string;
  selected_date?: string;
  day_record?: ClassDateInfo;
  date?: string;
  dow?: string;
  walkin_spots_available?: boolean;
  is_past?: boolean;
}

export interface ClassDateInfo {
  cancel_reason?: string;
  cancelled: boolean;
  date: string;
  is_class_subbed: boolean;
  subbing_instructor?: string;
  dow: string;
  id?: number;
  spots_available?: number;
  current_user_reservation?: Record<string, string | number>;
  is_past?: boolean;
  is_started?: boolean;
}

interface ClassCheckin {
  checkin_user_search_options: string[];
  checkin_via_name_search: boolean;
  checkin_via_barcode_image_scan: boolean;
  checkin_via_barcode_text_input: boolean;
  mins_before_class_to_show_reservation: number;
  mins_after_class_to_show_reservation: number;
  background_image_url: string;
  logo_url: string;
  logo_url___: string;
}

interface Configs {
  class_checkin: ClassCheckin;
}

interface Facility {
  id: number | string;
  name: string;
  label?: string;
  value?: string;
}

type ClassCategory = {
  id: number;
  category_name: string;
  university_id: number;
  active: number;
  deleted: number;
  file_id: number | null;
};

interface Data {
  client_name: string;
  facilities: Facility[];
  class_categories: ClassCategory[];
  timezone: string;
  configs: Configs;
  app_urls?: Record<string, string>;
}

export interface ClientInfoResponse {
  success: boolean;
  data: Data;
}

declare module "next-auth" {
  /**
   * Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface Session {
    user: {
      image: string;
      /** The user's postal address. */
      address: string;

      name: string;
    };
  }
}
