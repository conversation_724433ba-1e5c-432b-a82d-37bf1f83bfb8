"use client";

import { queryOptions, useQuery } from "@tanstack/react-query";

import { fetchClassesByOrgId } from "@/app/classes/[orgId]/actions/fetchClassesByOrgId";
import { sortBy } from "lodash/fp";

export const classesQueryOptions = (
  orgId: string,
  searchParams: Record<string, string | number>
) =>
  queryOptions({
    queryKey: [orgId, searchParams],
  });

export const useClassesData = (
  orgId: string,
  searchParams: Record<string, string | number>,
  xSource?: string
) => {
  const response = useQuery({
    queryKey: classesQueryOptions(orgId, searchParams).queryKey,
    staleTime: 30000,
    refetchInterval: 30 * 1000, // Fetch every 30 seconds
    refetchIntervalInBackground: true,
    queryFn: async ({ signal }) =>
      fetchClassesByOrgId({ orgId, params: searchParams, signal, xSource }),
    select: data => sortBy("start_time", data),
  });

  return response;
};
