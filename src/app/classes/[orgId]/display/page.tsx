import { fetchClientConfigs } from "@/common/api/fetchClientConfigs";
import { ClassesOverview } from "./overview";

export default async function Display({
  searchParams,
  params,
}: Readonly<{
  params: {
    orgId: string;
  };
  searchParams: Record<string, string | number>;
}>) {
  const { facilitiesOptions, clientLogo } = await fetchClientConfigs(
    params?.orgId
  );

  return (
    <div className='overflow-hidden'>
      <ClassesOverview
        searchParams={searchParams}
        facilitiesOptions={facilitiesOptions}
        clientLogo={clientLogo}
      />
    </div>
  );
}
