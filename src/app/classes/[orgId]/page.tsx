import { Footer } from "../../../components/custom/footer";

import { ClassesOverview } from "./modules/overview-block/overview";
import { fetchClientConfigs } from "@/common/api/fetchClientConfigs";
import { DateFormatPicker } from "@/components/custom/date-picker/date-picker";
import { ActivityFilters } from "@/components/custom/activity-filters";
import { LoginModal } from "@/components/custom/login-auth/login-modal";
import { ReservationsModal } from "../../../components/custom/reservations/reservations-modal";
import { ClassDownloadPDF } from "@/components/pdf-downloader-module/class-pdf-modal";

export default async function ClassesPage({
  params,
  searchParams,
}: Readonly<{
  params: {
    orgId: string;
  };
  searchParams: Record<string, string | number>;
}>) {
  const {
    facilitiesOptions,
    categoriesOptions,
    appUrls,
    configs,
    clientName,
    clientLogo,
    clientPdfHeader,
    clientBackground,
  } = await fetchClientConfigs(params.orgId);

  return (
    <div className='overflow-hidden mb-20'>
      <DateFormatPicker />
      <ActivityFilters
        facilitiesOptions={facilitiesOptions}
        categoriesOptions={categoriesOptions}
      />
      <LoginModal
        buttonColor={configs?.embed?.button_color}
        appUrls={appUrls}
        configs={configs}
        orgId={params.orgId}
      />

      <ClassesOverview searchParams={searchParams} />

      <ClassDownloadPDF
        facilitiesOptions={facilitiesOptions}
        categoriesOptions={categoriesOptions}
        clientName={clientName}
        clientId={params.orgId}
        clientLogo={clientLogo}
        clientPdfHeader={clientPdfHeader}
        appUrls={appUrls}
        clientBackground={clientBackground}
      />

      <ReservationsModal />

      <Footer
        appUrls={appUrls}
        infoText='Access Class Schedules on The Go! Download the app today:'
      />
    </div>
  );
}
