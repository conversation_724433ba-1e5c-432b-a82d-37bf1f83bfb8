import ReserveOverview from "./overview";
import { ScanQRCode } from "@/components/custom/scan-qr-code";
import { fetchClientConfigs } from "@/common/api/fetchClientConfigs";
import { Fragment } from "react";

export default async function ReservePage({
  params,
  searchParams,
}: Readonly<{
  params: {
    orgId: string;
  };
  searchParams: Record<string, string | number>;
}>) {
  const { appUrls } = await fetchClientConfigs(params.orgId);

  return (
    <Fragment>
      <ReserveOverview orgId={params.orgId} searchParams={searchParams} />
      <p className='mt-4 text-center'>
        Download our app for a better experience, manage your reservations, and
        get reminders for your upcoming reservations.
      </p>
      <ScanQRCode
        androidUrl={String(appUrls?.android)}
        iosUrl={String(appUrls?.ios)}
      />
    </Fragment>
  );
}
