"use client";
import {
  LoginForm,
  LoginSchema,
} from "@/components/custom/login-auth/login-form";
import { useState } from "react";
import { z } from "zod";
import { useClassesData } from "../display/useClassesData";
import { obtainDateFrame } from "../classes.utils";
import { useReserveMutation } from "../mutations/useReserveClass";
import { formatDate } from "@/common/common.utils";
import { useSession } from "@/components/custom/login-auth/auth-provider";
import { ReserveSuccessModal } from "../modules/overview-block/reserve-success-modal";
import { useStoreValue } from "@/app/StoreContext";

export default function ReserveOverview({
  orgId,
  searchParams,
}: Readonly<{
  orgId: string;
  searchParams: Record<string, string | number>;
}>) {
  const [isLoading, setIsLoading] = useState(false);

  const classData = useClassesData(orgId, searchParams)?.data?.[0];

  const { signIn, isError } = useSession();

  const { state: reservations, dispatch } = useStoreValue(
    state => state.reservation
  );

  const {
    mutate: handleReservation,
    isSuccess,
    isPending,
  } = useReserveMutation(() =>
    dispatch(() => ({
      reservation: { ...classData, date: searchParams.date ?? new Date() },
    }))
  );

  const handleSubmit = async (fieldValue: z.infer<typeof LoginSchema>) => {
    try {
      setIsLoading(true);
      await signIn({
        email: fieldValue.email,
        password: fieldValue.password,
        orgId: String(orgId),
      });
      if (!isError) {
        handleReservation({
          date: formatDate(
            searchParams.date ? String(searchParams?.date) : new Date()
          ),
          class_id: searchParams.class_id,
          type: "class",
        });
      }

      if (isError) {
        return;
      }
    } catch (error) {
      throw new Error("Something bad happened, try again");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className='container'>
      {classData && (
        <div className='items-center text-center'>
          {isSuccess ? (
            <h2 className='mt-4  text-3xl font-semibold'>
              Your Reservation is successful
            </h2>
          ) : (
            <h2 className='mt-4  text-3xl font-semibold'>
              {`Login to Reserve ${classData.name} Class`}
            </h2>
          )}
          <div className='flex items-center justify-center'>
            <div className='mt-4 flex-col text-center items-center w-full'>
              <div className='flex justify-between'>
                <p>Time:</p>
                <p className='ml-2 font-semibold'>
                  {obtainDateFrame(classData.start_time, classData.end_time)}
                </p>
              </div>
              <div className='flex justify-between'>
                <p>Location:</p>
                <p className='ml-2 font-semibold'>{classData.gym_name}</p>
              </div>
              <div className='flex justify-between'>
                <p>Room:</p>
                <p className='ml-2 font-semibold'>{classData.room_name}</p>
              </div>
              <div className='flex justify-between'>
                <p>Instructor:</p>
                <p className='ml-2 font-semibold'>{classData.instructor}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {reservations && (
        <ReserveSuccessModal
          reservations={reservations}
          onClose={() => dispatch(() => ({ reservation: null }))}
          paramDate={searchParams?.date as string}
        />
      )}

      {!isSuccess && (
        <LoginForm
          handleSubmit={handleSubmit}
          isError={isError}
          placeholder='Reserve'
          isLoading={isPending || isLoading}
        />
      )}
    </div>
  );
}
