"use client";

import { ClassDetailsResponse } from "@/app/classes/[orgId]/types";
import { DEFAULT_DATE_FORMAT, formatDate } from "@/common/common.utils";
import { BASE_API_URL_CLIENT } from "./constant";
import { add, format } from "date-fns";
import { getSession } from "@/components/custom/login-auth/auth-provider";

export const fetchReservationClasses = async (
  signal?: AbortSignal
): Promise<ClassDetailsResponse[]> => {
  const session = await getSession();
  const headers: { "Content-Type": string; authorization?: string } = {
    "Content-Type": "application/json",
  };

  headers.authorization = `Bearer ${session?.token}`;
  const reservationURL = `${BASE_API_URL_CLIENT}/reservations/mine`;

  try {
    const urlParams = new URLSearchParams({
      start_time: formatDate(),
      end_time: format(add(new Date(), { months: 6 }), DEFAULT_DATE_FORMAT), // Fetch for the next 6months
    }).toString();

    const rec = await fetch(`${reservationURL}?${urlParams}`, {
      headers,
      signal,
    });

    const data = await rec.json();

    return data?.data?.reservations || [];
  } catch (err) {
    throw new Error("Could not fetch classes");
  }
};
