"use client";

import { getSession } from "@/components/custom/login-auth/auth-provider";
import { BASE_API_URL_CLIENT } from "./constant";

export const leaveWait = async (id: number) => {
  const session = await getSession();

  const headers: { "Content-Type": string; authorization?: string } = {
    "Content-Type": "application/json",
  };

  if (session) {
    headers.authorization = `Bearer ${session?.token}`;
  }

  try {
    const response = await fetch(`${BASE_API_URL_CLIENT}/waitlists/exit`, {
      headers,
      method: "POST",
      body: JSON.stringify({ waitlist_id: id }),
    });
    return await response.json();
  } catch (err) {
    throw new Error("Sorry, there was a problem leaving waitlist");
  }
};
