// Instructors.tsx

import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { getInitials } from "../../../classes.utils";
import { cn } from "@/lib/utils";

interface InstructorsProps {
  primaryInstructor: string;
  subInstructor?: string;
  label?: string;
  className?: string;
}

export const Instructors = ({
  primaryInstructor,
  subInstructor,
  label = "Instructors:",
  className,
}: InstructorsProps) => {
  return (
    <div
      className={cn(
        "p-2 sm:p-4 md:p-4 pt-2 border-t border-gray-300",
        className
      )}
    >
      {label && (
        <h2 className='text-base sm:text-lg md:text-lg font-semibold mb-2 md:mb-2'>
          {label}
        </h2>
      )}
      <div className={cn("flex items-center mb-2 sm:mb-4", className)}>
        <Avatar className='mr-2 md:mr-3 h-8 w-8 sm:h-10 sm:w-10 md:h-10 md:w-10'>
          <AvatarFallback className='text-sm sm:text-base md:text-base'>
            {getInitials(primaryInstructor)}
          </AvatarFallback>
        </Avatar>
        <span className='font-semibold text-sm sm:text-base md:text-base'>
          {primaryInstructor}
        </span>
      </div>

      {subInstructor && (
        <div className={cn("flex items-center mb-2 sm:mb-4", className)}>
          <Avatar className='mr-2 md:mr-3 h-8 w-8 sm:h-10 sm:w-10 md:h-10 md:w-10'>
            <AvatarFallback className='text-sm sm:text-base md:text-base'>
              {getInitials(subInstructor)}
            </AvatarFallback>
          </Avatar>
          <span className='font-semibold text-sm sm:text-base md:text-base'>
            {subInstructor}
          </span>
          <i className='text-gray-500 ml-2 text-xs sm:text-sm'>Substitute</i>
        </div>
      )}
    </div>
  );
};
