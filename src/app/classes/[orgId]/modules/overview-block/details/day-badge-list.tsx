import React from "react";
import { ClassDetailsResponse } from "../../../types";
import { Badge } from "@/components/ui/badge";
import { Check, X } from "lucide-react";

const SHORT_DAY_NAMES = [
  { id: "mon", name: "<PERSON>" },
  { id: "tue", name: "<PERSON><PERSON>" },
  { id: "wed", name: "We<PERSON>" },
  { id: "thu", name: "Thu" },
  { id: "fri", name: "<PERSON><PERSON>" },
  { id: "sat", name: "<PERSON><PERSON>" },
  { id: "sun", name: "Sun" },
];

interface BadgeListProps {
  daysOfWeek?: { name: string; id: string }[];
  rowData: ClassDetailsResponse;
}

export const DayBadgeList = ({
  daysOfWeek = SHORT_DAY_NAMES,
  rowData,
}: BadgeListProps) => (
  <>
    {daysOfWeek.map(day => (
      <Badge
        key={day.name}
        variant='outline'
        className={`rounded-none py-1 pl-1 pr-0 w-14 border-2 border-r-2 ${
          rowData?.days_of_week?.includes(day.name.toLowerCase())
            ? "border-lime-500"
            : "border-gray-400"
        }`}
      >
        <div className='flex'>
          {rowData?.days_of_week?.includes(day.name.toLowerCase()) ? (
            <>
              <Check className='w-4 h-4' color='green' />
              <p className='font-bold text-xs'>{day.name}</p>
            </>
          ) : (
            <>
              <X className='w-4 h-4' color='gray' />
              <p className='font-bold text-xs text-gray-400'>{day.name}</p>
            </>
          )}
        </div>
      </Badge>
    ))}
  </>
);
