import { ClassDetailsResponse } from "../../../types";
import { useApplyStateToUrl } from "../../../../../../common/hooks/useApplyStateToUrl";
import { Button } from "@/components/ui/button";
import { Calendar, LogIn } from "lucide-react";
import { useSession } from "@/components/custom/login-auth/auth-provider";

export const DetailsFooter = ({
  rowData,
}: {
  rowData: ClassDetailsResponse;
}) => {
  const { data: sessionData } = useSession();
  const { is_waitlist_available, is_full } = rowData;

  const applyToUrl = useApplyStateToUrl();

  if (sessionData && is_full && is_waitlist_available) {
    return (
      <Button
        onClick={e => {
          e.stopPropagation();
          applyToUrl({ showLoginModal: true });
        }}
        className='text-black bg-yellow-500  ml-auto'
      >
        <span>Join Waitlist</span>
      </Button>
    );
  }

  if (sessionData && !is_full) {
    return (
      <Button className='text-white bg-blue-600 hover:bg-blue-600  ml-auto'>
        <Calendar className='mr-2 h-4 w-4' />
        Reserve
      </Button>
    );
  }

  if (!sessionData && !is_full) {
    return (
      <Button
        onClick={e => {
          e.stopPropagation();
          applyToUrl({ showLoginModal: true });
        }}
        className='text-white bg-blue-600 hover:bg-blue-600  ml-auto'
      >
        <LogIn className='mr-2 h-4 w-4' /> Log in to reserve
      </Button>
    );
  }

  return null;
};
