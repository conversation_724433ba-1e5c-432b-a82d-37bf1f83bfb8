import { Card, CardContent } from "@/components/ui/card";
import { format, parseISO } from "date-fns";
import { Calendar, Clock4 } from "lucide-react";
import Image from "next/image";
import { obtainDateFrame } from "../../../classes.utils";

interface ImageCardProps {
  imageSrc: string;
  startDay: string;
  startTime: string;
  endTime: string;
}

export const ImageCard = ({
  imageSrc,
  startDay,
  startTime,
  endTime,
}: ImageCardProps) => (
  <div className='relative h-[200px] md:h-[220px] lg:h-[230px] mt-0 md:rounded-lg md:overflow-hidden'>
    <Image
      className='absolute inset-0 h-full z-3 w-full rounded md:rounded-lg object-cover'
      src={imageSrc}
      height={300}
      width={500}
      alt='upace-image'
      priority
    />
    <Card className='absolute bottom-2 left-4 md:bottom-3 md:left-4 border-r-0 border-b-0 h-[17] rounded shadow-md'>
      <CardContent className='p-3 md:p-4'>
        <div className='flex gap-3 items-center'>
          <Calendar className='h-4 w-4 md:h-5 md:w-5 text-[#009DC4]' />
          <p className='font-light text-sm md:text-base'>
            {format(
              startDay ? parseISO(startDay) : new Date().toISOString(),
              "EEEE, MMMM d"
            )}
          </p>
        </div>
        <div className='flex gap-3 items-center mt-2 md:mt-3'>
          <Clock4 className='h-4 w-4 md:h-5 md:w-5 text-[#009DC4]' />
          <p className='font-light text-sm md:text-base'>
            {obtainDateFrame(startTime, endTime)}
          </p>
        </div>
      </CardContent>
    </Card>
  </div>
);
