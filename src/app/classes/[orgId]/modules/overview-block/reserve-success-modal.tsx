import { useStoreValue } from "@/app/StoreContext";
import { <PERSON><PERSON>, DialogContent, DialogHeader } from "@/components/ui/dialog";
import { Share2, CheckCircle2 } from "lucide-react";

import { AddToCalendarButton } from "add-to-calendar-button-react";

import { CopyToClipboard } from "react-copy-to-clipboard";

import { format, parseISO } from "date-fns";
import { Button } from "@/components/ui/button";
import { obtainDateFrame } from "../../classes.utils";
import { useState } from "react";
import { styled } from "styled-components";
import tw from "twin.macro";
import { useSession } from "@/components/custom/login-auth/auth-provider";

const StyledHeader = styled(DialogHeader)<{ color?: string }>`
  ${tw`bg-[#00a1c5] p-4 pt-0 flex flex-col`};
  background-color: ${({ color }) => color};
`;

const StyledText = styled.h1<{ color?: string }>`
  color: ${({ color }) => color};
`;

export const ReserveSuccessModal = ({
  paramDate,
  reservations,
  onClose,
  embedType = "classes",
}: {
  reservations: Record<string, unknown>;
  onClose: () => void;
  paramDate?: string;
  embedType?: "classes" | "equipment";
}) => {
  const [isCopied, setIsCopied] = useState(false);

  const { state: configs } = useStoreValue(state => state.configs);

  const { data } = useSession();

  const date = paramDate
    ? parseISO(paramDate)
    : parseISO(new Date().toISOString());

  const formattedDate = format(date, "yyyy-MM-dd");

  return (
    <Dialog
      open={Boolean(reservations)}
      onOpenChange={() => {
        onClose();
        setIsCopied(false);
      }}
    >
      <DialogContent
        color='white'
        className='lg:w-[350px] flex flex-col text-center w-full p-0 m-0 border-0 rounded-sm'
      >
        <StyledHeader
          color={configs?.accent_color}
          className='flex justify-center items-center gap-4 w-full h-14 relative'
        >
          <CheckCircle2
            color={configs?.accent_color}
            className='absolute bottom-[-20px] left-1/2 transform -translate-x-1/2 bg-white w-12 h-12 rounded-full p-0 m-0'
          />
        </StyledHeader>
        <section>
          <StyledText
            color={configs?.accent_color}
            className='mt-8 font-extrabold text-3xl mb-2'
          >
            Success!
          </StyledText>
          <p>Reservation Complete for:</p>
          <p className='mt-5 mb-5'>{data?.name}</p>
        </section>
        <section>
          <StyledText
            color={configs?.accent_color}
            className='font-extrabold mb-2'
          >
            Reservation Details
          </StyledText>
          {Boolean(reservations?.name) && (
            <p className='font-bold'>{String(reservations?.name)}</p>
          )}
          {Boolean(reservations?.instructor) && (
            <p className='mb-2'>{String(reservations?.instructor)}</p>
          )}

          {Boolean(reservations?.attending_persons) && (
            <p className='mb-2 mt-2'>{`${String(reservations?.attending_persons)} person(s) attending`}</p>
          )}
          <p className='mb-2'>{format(date, "EEEE, MMMM d")}</p>
          <p className='mb-2'>
            {obtainDateFrame(
              String(reservations?.start_time),
              String(reservations?.end_time)
            )}
          </p>
          <p className='mb-2'>{String(reservations?.gym_name)}</p>
          <p className='mb-2'>{String(reservations?.room_name)}</p>
        </section>
        <div className='flex flex-col text-center items-center gap-2 mb-10 mt-4'>
          <AddToCalendarButton
            name={String(reservations?.name)}
            startDate={formattedDate}
            startTime={String(reservations?.start_time)}
            endTime={String(reservations?.end_time)}
            options={["Apple", "Google", "Yahoo", "iCal"]}
            location={String(reservations?.gym_name)}
            // useUserTZ
            hideCheckmark
            size='4'
            inline
            description={`<div>
              <p>TIME:  ${obtainDateFrame(String(reservations?.start_time), String(reservations?.end_time))}</p>
              <p>LOCATION: ${reservations?.gym_name}</p>
              <p>ROOM: ${reservations?.gym_name} Center</p>
              <p>INSTRUCTOR: ${reservations?.instructor ?? "-"}</p>
            </div>`}
          />
          <CopyToClipboard
            text={`Hey! I just signed up for ${reservations?.name}  on ${formattedDate} at ${obtainDateFrame(String(reservations?.start_time), String(reservations?.end_time))} . ${embedType === "classes" ? `To join me click here: ${window?.location?.origin}${window?.location?.pathname}?selectedClass=${reservations?.id}` : `To join me click here: ${window?.location?.origin}${window?.location?.pathname}`}  `}
            onCopy={() => setIsCopied(true)}
          >
            <Button
              variant={"secondary"}
              disabled={isCopied}
              className='w-40 !text-black'
            >
              <div className='flex justify-between w-full'>
                <Share2 className='w-5' />
                {isCopied ? "Copied" : " Share"}
              </div>
            </Button>
          </CopyToClipboard>
        </div>
      </DialogContent>
    </Dialog>
  );
};
