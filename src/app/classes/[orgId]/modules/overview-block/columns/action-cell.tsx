"use client";
import { ClassDetailsResponse } from "../../../types";
import { Calendar } from "lucide-react";

import { useSearchParams } from "next/navigation";

import { useStoreValue } from "@/app/StoreContext";

import { ReserveButton } from "../../../components/reserve-button";
import { CancelButton } from "../../../../../../components/custom/cancel-button";
import { LeaveWaitlistButton } from "../../../components/leave-reservation";
import Link from "next/link";
import { ActionButton } from "@/components/custom/action-button";
import { useSession } from "@/components/custom/login-auth/auth-provider";

const SessionAvailable = ({ data }: { data: ClassDetailsResponse }) => {
  const {
    is_waitlist_available,
    is_full,
    current_user_reservation,
    current_user_waitlist,
  } = data;

  const date = useSearchParams().get("date");

  const { dispatch } = useStoreValue();

  if (current_user_waitlist) {
    return (
      <LeaveWaitlistButton
        id={current_user_waitlist?.id}
        positionIndex={current_user_waitlist?.position_index}
      />
    );
  }

  if (is_full && is_waitlist_available && !current_user_reservation) {
    return (
      <ReserveButton
        data={data}
        date={data?.date ?? date}
        className='!border-lightOrange-500 !text-lightOrange-500'
        label='Join Waitlist'
      />
    );
  }

  if (current_user_reservation) {
    return (
      <CancelButton
        onClick={() =>
          dispatch(() => ({
            cancelReservation: { ...current_user_reservation },
          }))
        }
      />
    );
  }

  if (!is_full) {
    return <ReserveButton data={data} date={data?.date ?? date} />;
  }

  return null;
};

export const SessionNotAvailable = ({
  data,
}: {
  data: ClassDetailsResponse;
}) => {
  const { is_waitlist_available, is_full } = data;

  const { dispatch } = useStoreValue(state => state.shouldShowLoginModal);

  if (is_full && is_waitlist_available) {
    return (
      <ActionButton
        onClick={() => dispatch(() => ({ shouldShowLoginModal: true }))}
        icon={<Calendar />}
        className='border-blue-500'
      >
        Login for waitlist
      </ActionButton>
    );
  }

  return (
    <ActionButton
      onClick={() => dispatch(() => ({ shouldShowLoginModal: true }))}
      icon={<Calendar />}
      className='border-blue-500'
    >
      Login to reserve
    </ActionButton>
  );
};

export const LoginAction = ({ data }: { data: ClassDetailsResponse }) => {
  const { data: sessionData } = useSession();

  const { state: configs } = useStoreValue(state => state.configs);

  if (data?.is_past) {
    return (
      <ActionButton
        variant='default'
        isDisabled
        className='!bg-gray-200 text-black cursor-not-allowed'
      >
        Past Class – Reservation Closed
      </ActionButton>
    );
  }

  if (data.is_full && data.walkin_spots_available) {
    return (
      <ActionButton
        variant='default'
        isDisabled
        className='!bg-gray-200 text-black cursor-not-allowed'
      >
        Walk-ins Spots Available
      </ActionButton>
    );
  }

  if (data?.cancelled) {
    return (
      <ActionButton
        variant='default'
        isDisabled
        className='!bg-gray-200 !text-black cursor-not-allowed'
      >
        Cancelled
      </ActionButton>
    );
  }

  return sessionData ? (
    <div className='flex flex-col'>
      <SessionAvailable data={data} />
      {Boolean(data.is_sgt && configs?.sgtPurchaseUrl) && (
        <Link
          onClick={e => e.stopPropagation()}
          href={configs?.sgtPurchaseUrl ?? ""}
          target='__blank'
          className='text-center font-thin text-xs mb-1 mt-2'
          style={{
            color: configs?.sgt_color ?? "",
            fontWeight: "bold",
          }}
        >
          Purchase here
        </Link>
      )}
    </div>
  ) : (
    <div className='flex flex-col'>
      <SessionNotAvailable data={data} />
      {Boolean(data.is_sgt && configs?.sgtPurchaseUrl) && (
        <Link
          onClick={e => e.stopPropagation()}
          href={configs?.sgtPurchaseUrl ?? ""}
          target='__blank'
          className='text-center font-thin text-xs mb-1 mt-1'
          style={{
            color: configs?.sgt_color ?? "",
            fontWeight: "bold",
          }}
        >
          Purchase here
        </Link>
      )}
    </div>
  );
};
