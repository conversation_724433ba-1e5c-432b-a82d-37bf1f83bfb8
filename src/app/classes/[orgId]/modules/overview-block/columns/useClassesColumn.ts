"use client";

import { isMobileOnly } from "react-device-detect";
import { useMobileClassesColumns } from "./useMobileClassesColumns";
import { useClassesTableColumns } from "./useClassesTablecolumns";

export const useClassesColumn = () => {
  const mobileColumns = useMobileClassesColumns();
  const desktopColumns = useClassesTableColumns();

  if (isMobileOnly) {
    return mobileColumns;
  }

  return desktopColumns;
};
