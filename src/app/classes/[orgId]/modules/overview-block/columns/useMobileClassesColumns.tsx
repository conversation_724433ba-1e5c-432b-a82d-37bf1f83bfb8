"use client";

import { ColumnDef } from "@tanstack/react-table";
import { ClassDetailsResponse } from "../../../types";
import { useMemo } from "react";

import { LoginAction } from "./action-cell";
import { obtainDateFrame } from "../../../classes.utils";
import { getSpotsAvailable, StyledCell } from "./useClassesTablecolumns";
import { useStoreValue } from "@/app/StoreContext";

const DetailsCell = ({ data }: { data: ClassDetailsResponse }) => {
  const { instructor, gym_name, start_time, end_time, name } = data;

  return (
    <div className='p-4 rounded-md bg-white'>
      <p className='font-extrabold whitespace'>
        {obtainDateFrame(start_time, end_time)}
      </p>
      <p className='font-normal mt-2 whitespace'>{name}</p>
      <p className='font-normal mt-2 whitespace'>{instructor}</p>
      <p className='font-normal mt-2 whitespace'>{gym_name}</p>
    </div>
  );
};

export const useMobileClassesColumns = () => {
  const { state: embedConfigs } = useStoreValue(state => state.configs);

  return useMemo<ColumnDef<ClassDetailsResponse>[]>(
    () => [
      {
        accessorFn: row =>
          `${row.instructor}, ${row.gym_name},${row.name}, ${obtainDateFrame(row.start_time, row.end_time)}`,
        header: "Details",
        cell: ({ row }) => (
          <StyledCell
            color={row.original.is_sgt ? embedConfigs?.sgt_color : undefined}
          >
            <DetailsCell data={row.original} />
          </StyledCell>
        ),
      },

      {
        id: "action",
        header: "Action",
        cell: ({ row }) => {
          return (
            <div>
              <StyledCell
                color={
                  row.original.is_sgt ? embedConfigs?.sgt_color : undefined
                }
                className='text-sm mb-2'
              >
                {getSpotsAvailable(row.original)}
              </StyledCell>
              <LoginAction data={row?.original} />
            </div>
          );
        },
      },
    ],
    [embedConfigs?.sgt_color]
  );
};
