"use client";

import { DataTable } from "@/components/data-table/data-table";
import { Fragment, useCallback, useMemo } from "react";

import { ClassDetailsResponse } from "../../types";
import { ColumnDetails } from "./details/details";
import { useClassesColumn } from "./columns/useClassesColumn";
import { useApplyStateToUrl } from "@/common/hooks/useApplyStateToUrl";
import { useParams, useSearchParams } from "next/navigation";
import { useClassesData } from "../../queries/useClassesData";

export const ClassesDataTable = ({
  searchParams,
}: {
  searchParams: Record<string, string | number>;
}) => {
  const columns = useClassesColumn();

  const applyToUrl = useApplyStateToUrl();

  const { orgId } = useParams();

  const { data = [], isPending: isLoading } = useClassesData(
    orgId as string,
    searchParams
  );

  const selectedClass = Number(useSearchParams().get("selectedClass"));

  const handleRowClick = useCallback(
    (selected: ClassDetailsResponse) => {
      applyToUrl({ selectedClass: selected.id });
    },
    [applyToUrl]
  );

  const handleOpen = useCallback(
    (open: boolean) => {
      if (open) {
        return applyToUrl({ selectedClass });
      }
      return applyToUrl({ selectedClass: null });
    },
    [applyToUrl, selectedClass]
  );

  const selectedData = useMemo(
    () => data.find(item => item.id === selectedClass),
    [data, selectedClass]
  );

  return (
    <Fragment>
      <DataTable
        isLoading={isLoading}
        onRowClick={handleRowClick}
        data={data}
        columns={columns}
        searchPlaceholder='Search by class, name, room, time or instructor'
      />

      {selectedData && (
        <ColumnDetails
          rowData={selectedData}
          isOpen={Boolean(selectedClass)}
          onOpen={handleOpen}
          selectedDate={searchParams?.date as string}
        />
      )}
    </Fragment>
  );
};
