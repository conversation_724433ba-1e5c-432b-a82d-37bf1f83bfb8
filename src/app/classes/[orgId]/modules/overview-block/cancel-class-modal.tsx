"use client";
import { Dialog, DialogContent } from "@/components/ui/dialog";

import { But<PERSON> } from "@/components/ui/button";

import { useStoreValue } from "@/app/StoreContext";
import { CancelButton } from "../../../../../components/custom/cancel-button";

export const CancelReservationModal = ({
  onClose,
}: {
  onClose?: () => void;
}) => {
  const { state: cancelReservation, dispatch } = useStoreValue(
    state => state.cancelReservation
  );

  return (
    <Dialog
      open={Boolean(cancelReservation)}
      onOpenChange={() => dispatch(() => ({ cancelReservation: null }))}
    >
      <DialogContent className='w-[300px] flex flex-col text-center font-bold'>
        <p className='text-base mt-4'>
          Would you like to cancel your reservation?
        </p>
        <CancelButton
          label='Yes, Cancel Reservation'
          id={cancelReservation?.id as number}
          className='!w-full'
          onSuccess={onClose}
        />
        <Button
          onClick={() => dispatch(() => ({ cancelReservation: null }))}
          className='bg-blue-500 w-full'
        >
          No, Keep Reservation
        </Button>
      </DialogContent>
    </Dialog>
  );
};
