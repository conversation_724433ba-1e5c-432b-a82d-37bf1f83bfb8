import { fetchClientConfigs } from "@/common/api/fetchClientConfigs";
import { SchedulePage } from "./schedule";

export default async function Schedule({
  params,
  searchParams,
}: Readonly<{
  params: {
    orgId: string;
  };
  searchParams: Record<string, string | number>;
}>) {
  const {
    facilitiesOptions,
    categoriesOptions,
    appUrls,
    clientName,
    clientLogo,
    clientPdfHeader,
  } = await fetchClientConfigs(params.orgId);

  return (
    <div className='overflow-hidden h-screen'>
      <SchedulePage
        facilitiesOptions={facilitiesOptions}
        categoriesOptions={categoriesOptions}
        clientName={clientName}
        clientId={params.orgId}
        clientLogo={clientLogo}
        clientPdfHeader={clientPdfHeader}
        appUrls={appUrls}
        searchParams={searchParams}
      />
    </div>
  );
}
