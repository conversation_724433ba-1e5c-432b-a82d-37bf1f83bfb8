"use client";

import { useMemo } from "react";
import { ScheduleTable } from "../../../../components/custom/schedule-table";
import { formatTime, obtainDateFrame } from "../classes.utils";
import { ClassDetailsResponse } from "../types";
import { ColumnBadge, DefaultStatus } from "@/components/custom/column-badge";

const CalendarView = ({
  row,
  column,
}: {
  row: ClassDetailsResponse;
  column: string;
}) => {
  if (!row?.days_of_week?.includes(column)) {
    return <div className='flex-wrap'></div>;
  }

  const selectedDate = row.date_info?.find(({ dow }) => dow === column);

  return (
    <div className={"bg-gray-100 h-full"}>
      {selectedDate?.cancelled && (
        <ColumnBadge
          className='font-bold'
          getStatus={() => DefaultStatus.CANCELLED}
        />
      )}
      <div className={`pt-4 ${selectedDate?.cancelled ? "line-through" : ""}`}>
        <p className='block text-sm font-medium text-gray-700'>
          {obtainDateFrame(row.start_time, row.end_time)}
        </p>

        <p className='block text-lg font-semibold text-gray-900 my-1'>
          {row.name}
        </p>

        <div className='flex justify-between'>
          <p className='block text-sm font-medium text-gray-700'>
            {selectedDate?.is_class_subbed
              ? selectedDate?.subbing_instructor
              : row.instructor}
          </p>

          {selectedDate?.is_class_subbed && (
            <p style={{ fontSize: 8, fontWeight: "bold", color: "red" }}>
              SUBSTITUTE
            </p>
          )}
        </div>
        <span className='block text-sm font-medium text-gray-700'>
          {row.room_name}
        </span>
      </div>
    </div>
  );
};

function defaultIsGreyed(
  item: ClassDetailsResponse,
  columnId: string
): boolean {
  return Boolean(item?.date_info?.find(({ dow }) => dow === columnId));
}

export const Calendar = ({
  data,
  color,
  category,
  activity,
  isLoading,
}: {
  data: ClassDetailsResponse[];
  color?: string;
  activity?: string;
  category?: string;
  isLoading?: boolean;
}) => {
  const columns = useMemo(
    () => [
      {
        id: "",
        label: "",
        renderCell: () => "",
      },
      {
        id: "sun",
        label: "SUNDAY",
        renderCell: (item: ClassDetailsResponse, column: string) => (
          <CalendarView row={item} column={column} />
        ),
        isGreyed: defaultIsGreyed,
      },
      {
        id: "mon",
        label: "MONDAY",
        renderCell: (item: ClassDetailsResponse, column: string) => (
          <CalendarView row={item} column={column} />
        ),
        isGreyed: defaultIsGreyed,
      },
      {
        id: "tue",
        label: "TUESDAY",
        renderCell: (item: ClassDetailsResponse, column: string) => (
          <CalendarView row={item} column={column} />
        ),
        isGreyed: defaultIsGreyed,
      },
      {
        id: "wed",
        label: "WEDNESDAY",
        renderCell: (item: ClassDetailsResponse, column: string) => (
          <CalendarView row={item} column={column} />
        ),
        isGreyed: defaultIsGreyed,
      },
      {
        id: "thu",
        label: "THURSDAY",
        renderCell: (item: ClassDetailsResponse, column: string) => (
          <CalendarView row={item} column={column} />
        ),
        isGreyed: defaultIsGreyed,
      },
      {
        id: "fri",
        label: "FRIDAY",
        renderCell: (item: ClassDetailsResponse, column: string) => (
          <CalendarView row={item} column={column} />
        ),
        isGreyed: defaultIsGreyed,
      },
      {
        id: "sat",
        label: "SATURDAY",
        renderCell: (item: ClassDetailsResponse, column: string) => (
          <CalendarView row={item} column={column} />
        ),
        isGreyed: defaultIsGreyed,
      },
    ],
    []
  );

  return (
    <div className='p-6 mb-20'>
      <ScheduleTable
        columns={columns}
        data={data || []}
        color={color}
        renderEmptyCell={item => <>{formatTime(item?.start_time)}</>}
        isLoading={isLoading}
        emptyStateMessage={`No Classes for Facility : ${activity ?? "All facilities"} and Schedule Type: ${category ?? "All categories"}`}
      />
    </div>
  );
};
