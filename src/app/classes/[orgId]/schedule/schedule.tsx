"use client";

import { ClassPDFCreator } from "@/components/pdf-downloader-module/class-pdf-creator";
import { BaseSelect, Option } from "@/components/ui/select";
import { useClassesWeeklyData } from "../queries/useClassesWeeklyData";
import { usePDFFilters } from "@/components/pdf-downloader-module/usePDFFilters";
import { formatDate, parseISO } from "date-fns";
import { ALL_OPTION } from "@/components/custom/activity-filters";
import { ChevronLeft, ChevronRight, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useApplyStateToUrl } from "@/common/hooks/useApplyStateToUrl";
import { useEffect, useMemo, useRef } from "react";
import { PDFDownloader } from "@/components/pdf-downloader-module/downloader";
import { Calendar } from "./calendar";

export const SchedulePage = ({
  categoriesOptions,
  facilitiesOptions,
  clientId,
  clientLogo,
  clientName,
  appUrls,
  searchParams,
  clientPdfHeader,
}: {
  categoriesOptions: Option[];
  facilitiesOptions: Option[];
  clientName?: string;
  clientId?: string;
  clientLogo?: string;
  clientPdfHeader?: string;
  appUrls?: Record<string, string>;
  searchParams: Record<string, string | number>;
}) => {
  const {
    startWeek,
    endOfWeek,
    configs,
    onNextWeek,
    onPreviousWeek,
    canGoPrev,
  } = usePDFFilters();

  const { data, isPending } = useClassesWeeklyData(
    clientId as string,
    {
      gym_id:
        searchParams?.gym_id === ALL_OPTION || !searchParams?.gym_id
          ? ""
          : String(searchParams?.gym_id),
      class_category_id:
        searchParams?.class_category_id === ALL_OPTION ||
        !searchParams?.class_category_id
          ? ""
          : String(searchParams?.class_category_id),
      start_date: startWeek,
      end_date: endOfWeek,
    },
    Boolean(searchParams.gym_id)
  );

  const applyToUrl = useApplyStateToUrl();

  const defaultValue = useRef({ gym_id: searchParams?.gym_id });
  const applyToUrlRef = useRef(applyToUrl);
  const facilitiesRef = useRef(facilitiesOptions);

  useEffect(() => {
    if (!defaultValue.current?.gym_id) {
      applyToUrlRef.current({
        gym_id: facilitiesRef.current?.[1]?.value,
      });
    }
  }, []);

  const activity = facilitiesOptions.find(
    fac => fac.value === searchParams?.gym_id
  )?.label;

  const category = categoriesOptions.find(
    cat => cat.value === searchParams?.class_category_id
  )?.label;

  const filteredFacility = useMemo(
    () => facilitiesRef.current?.filter(fac => fac.value !== ALL_OPTION),
    []
  );

  const handleChange = (option: Option, type: string) =>
    applyToUrl({ [type]: option?.value });

  return (
    <div className='flex-col'>
      <div className='container my-5 flex flex-col lg:flex-row gap-4 lg:items-center justify-center mx-auto'>
        <BaseSelect
          className='w-full lg:w-[250px]'
          onChange={val => handleChange(val, "gym_id")}
          placeholder='Select Location'
          options={filteredFacility}
          value={String(searchParams?.gym_id) ?? ""}
          name='gym_id'
        />
        <BaseSelect
          className='w-full lg:w-[250px]'
          onChange={val => handleChange(val, "class_category_id")}
          placeholder={"Select Categories"}
          options={categoriesOptions}
          value={String(searchParams?.class_category_id) ?? ""}
          name='class_category_id'
        />

        <PDFDownloader
          document={
            <ClassPDFCreator
              data={data || []}
              clientName={clientName}
              weekPeriod={`${startWeek} - ${endOfWeek}`}
              activityType={activity}
              configs={configs}
              logo={clientLogo}
              header={clientPdfHeader}
              category={category}
              androidUrl={appUrls?.android}
              iosUrl={appUrls?.ios}
            />
          }
          fileName={`${clientName}-schedule-${startWeek}-${endOfWeek}`}
        >
          <Button className='w-full' disabled={Boolean(!data?.length)}>
            Download Weekly PDF
          </Button>
        </PDFDownloader>
      </div>
      <p className='text-center pt-4 font-bold  mb-4 text-2xl'>
        Schedule for Week
      </p>
      <div className='flex items-center justify-center w-full'>
        <button
          title='previous week'
          type='button'
          disabled={!canGoPrev}
          onClick={() => onPreviousWeek()}
          style={{ color: configs?.accent_color }}
          className={`transition ease-in-out duration-150 ${!canGoPrev ? "opacity-50 cursor-not-allowed" : "hover:opacity-75"}`}
        >
          <ChevronLeft width={50} height={50} />
        </button>

        <p className='text-center pt-4 font-bold  mb-4 text-2xl'>
          {formatDate(parseISO(startWeek), "MMMM dd")} - {""}
          {formatDate(parseISO(endOfWeek), "MMMM dd, yyyy")}
        </p>
        <button
          style={{ color: configs?.accent_color }}
          title='next week'
          type='button'
          onClick={() => onNextWeek()}
        >
          <ChevronRight width={50} height={50} />
        </button>
      </div>

      <div className='flex justify-center'>
        {isPending && (
          <div className='flex justify-center'>
            <Loader2 className='animate-spin font-bold text-5xl' />
          </div>
        )}
      </div>
      <Calendar
        activity={activity}
        category={category}
        data={data || []}
        color={configs?.accent_color}
        isLoading={isPending}
      />
    </div>
  );
};
