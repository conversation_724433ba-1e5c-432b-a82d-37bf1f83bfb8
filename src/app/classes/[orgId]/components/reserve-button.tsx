import { useStoreValue } from "@/app/StoreContext";
import { ClassDetailsResponse } from "../types";
import { useReserveMutation } from "../mutations/useReserveClass";
import { formatDate } from "@/common/common.utils";
import { Calendar } from "lucide-react";
import { cn } from "@/lib/utils";
import { ActionButton } from "@/components/custom/action-button";

export const ReserveButton = ({
  data,
  date,
  className,
  label = "Reserve",
}: {
  data: ClassDetailsResponse;
  date?: string | null;
  className?: string;
  label?: string;
}) => {
  const { dispatch } = useStoreValue();

  const { mutate: handleReservation, isPending } = useReserveMutation(() =>
    dispatch(() => ({
      reservation: { ...data, date },
    }))
  );

  const isReservationAllowed = data?.allow_reservations;

  const handleReserve = () => {
    handleReservation({
      date: formatDate(date),
      class_id: data.id,
      type: "class",
      is_virtual: Boolean(data.is_virtual),
    });
  };

  return (
    <ActionButton
      icon={!isReservationAllowed ? undefined : <Calendar />}
      className={cn("border-blue-500", className)}
      onClick={handleReserve}
      isLoading={isPending}
      isDisabled={!isReservationAllowed}
      variant={!isReservationAllowed ? "destructive" : "outline"}
      style={!isReservationAllowed ? { color: "red" } : undefined}
      {...data}
    >
      {!isReservationAllowed ? "More Info" : label}
    </ActionButton>
  );
};
