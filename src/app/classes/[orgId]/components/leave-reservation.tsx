import { ActionButton } from "@/components/custom/action-button";
import { useLeaveWaitlist } from "../mutations/useLeaveWaitlist";

export const LeaveWaitlistButton = ({
  id,
  positionIndex,
}: {
  id?: number | string;
  positionIndex?: number | string;
}) => {
  const { mutate: handleLeaveWaitlist, isPending: isLoading } =
    useLeaveWaitlist();

  return (
    <div>
      <ActionButton
        className='!border-lightOrange-500 !text-lightOrange-500'
        variant='outline'
        onClick={() => handleLeaveWaitlist(Number(id))}
        isLoading={isLoading}
      >
        Leave Waitlist
      </ActionButton>
      <p className='mt-2 text-xs'>{`Position: ${positionIndex}`}</p>
    </div>
  );
};
