import { fetchClientConfigs } from "@/common/api/fetchClientConfigs";

import { ClassPDFOverview } from "./overview";

export default async function ClassesPage({
  params,
}: Readonly<{
  params: {
    orgId: string;
  };
  searchParams: Record<string, string | number>;
}>) {
  const {
    facilitiesOptions,
    categoriesOptions,
    appUrls,
    clientName,
    clientLogo,
    clientPdfHeader,
    clientBackground,
  } = await fetchClientConfigs(params.orgId);

  return (
    <ClassPDFOverview
      facilitiesOptions={facilitiesOptions}
      categoriesOptions={categoriesOptions}
      clientName={clientName}
      clientId={params.orgId}
      clientLogo={clientLogo}
      clientPdfHeader={clientPdfHeader}
      appUrls={appUrls}
      clientBackground={clientBackground}
    />
  );
}
