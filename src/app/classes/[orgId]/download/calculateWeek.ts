import { formatDate } from "@/common/common.utils";
import { isSameWeek, startOfWeek, lastDayOfWeek, parseISO } from "date-fns";

type WeekRange = {
  startWeek: string;
  endOfWeek: string;
};

export function checkDateInCurrentWeek(
  selectedDate?: string | null,
  currentWeek?: Date
): WeekRange {
  const now = parseISO(new Date(currentWeek ?? "").toISOString());

  if (!selectedDate) {
    return {
      startWeek: formatDate(startOfWeek(now)),
      endOfWeek: formatDate(lastDayOfWeek(now)),
    };
  }

  const passedDate = parseISO(selectedDate);

  const isInCurrentWeek = isSameWeek(selectedDate, now);

  if (isInCurrentWeek) {
    return {
      startWeek: formatDate(startOfWeek(now)),
      endOfWeek: formatDate(lastDayOfWeek(now)),
    };
  }

  return {
    startWeek: formatDate(startOfWeek(passedDate)),
    endOfWeek: formatDate(lastDayOfWeek(passedDate)),
  };
}
