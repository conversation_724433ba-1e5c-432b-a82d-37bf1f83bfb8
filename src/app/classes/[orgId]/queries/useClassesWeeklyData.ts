"use client";

import { useQuery } from "@tanstack/react-query";
import { fetchClassesByOrgId } from "../actions/fetchClassesByOrgId";
import { useSession } from "@/components/custom/login-auth/auth-provider";
import { sortBy } from "lodash/fp";

export const GET_WEEKLY_CLASSES = "GET_WEEKLY_CLASSES";

export const useClassesWeeklyData = (
  orgId: string,
  searchParams: Record<string, string | number>,
  enabledRequest: boolean = false
) => {
  const { data: sessionData } = useSession();

  return useQuery({
    queryKey: [orgId, searchParams, sessionData?.token],
    enabled: enabledRequest,
    refetchOnWindowFocus: false,
    refetchInterval: false,
    queryFn: async ({ signal }) =>
      fetchClassesByOrgId({
        orgId,
        params: searchParams,
        signal,
        isExcludeParams: true,
      }),
    select: data => sortBy(["start_time", "name"], data),
  });
};
