"use client";

import { useQuery } from "@tanstack/react-query";
import { fetchReservationClasses } from "../actions/fetchUserReservation";
import { fetchWaitlistClasses } from "../actions/fetchWaitlistClasses";
import { useSession } from "@/components/custom/login-auth/auth-provider";

export const GET_USER_RESERVATIONS = "GET_USER_RESERVATIONS";
export const GET_USER_WAITLIST = "GET_USER_WAITLIST";

export const useUserReservations = (enableRequest?: boolean) => {
  const { data: sessionData } = useSession();

  const wailtlist = useQuery({
    queryKey: [GET_USER_WAITLIST, sessionData?.token],
    enabled: Boolean(enableRequest),
    queryFn: async ({ signal }) => fetchWaitlistClasses(signal),
  });

  const reservations = useQuery({
    queryKey: [GET_USER_RESERVATIONS, sessionData?.token],
    enabled: <PERSON><PERSON><PERSON>(enableRequest),
    queryFn: async ({ signal }) => fetchReservationClasses(signal),
  });

  const data = [...(reservations.data ?? []), ...(wailtlist.data ?? [])]?.sort(
    (a, b) => Number(b?.start_time) - Number(a?.start_time)
  );

  return {
    ...reservations,
    data,
  };
};
