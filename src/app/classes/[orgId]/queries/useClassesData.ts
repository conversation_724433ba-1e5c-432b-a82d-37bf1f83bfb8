"use client";

import { queryOptions, useQuery } from "@tanstack/react-query";
import { fetchClassesByOrgId } from "../actions/fetchClassesByOrgId";
import { useStoreValue } from "@/app/StoreContext";
import { useEffect, useRef } from "react";
import { formatTime } from "../classes.utils";
import { ALL_OPTION } from "@/components/custom/activity-filters";
import { useSession } from "@/components/custom/login-auth/auth-provider";
import { sortBy } from "lodash/fp";

export const classesQueryOptions = (
  orgId: string,
  searchParams: Record<string, string | number>,
  token?: string | null
) =>
  queryOptions({
    queryKey: [orgId, searchParams, token],
  });

export const useClassesData = (
  orgId: string,
  searchParams: Record<string, string | number>
) => {
  const { dispatch } = useStoreValue();

  const { data: sessionData } = useSession();
  const response = useQuery({
    queryKey: classesQueryOptions(orgId, searchParams, sessionData?.token)
      .queryKey,
    queryFn: async ({ signal }) =>
      fetchClassesByOrgId({ orgId, params: searchParams, signal }),
    select: data => sortBy("start_time", data),
  });

  const refData = useRef(response.data);

  // Refactor me
  useEffect(() => {
    if (response?.data !== refData.current) {
      refData.current = response.data;
      return dispatch(() => {
        const startTimeOptions = refData.current?.map(item => ({
          value: item.start_time,
          label: formatTime(item.start_time),
        }));
        const endTimeOptions = refData.current?.map(item => ({
          value: item.end_time,
          label: formatTime(item.end_time),
        }));

        const newStartTimeOptions = [
          { value: ALL_OPTION, label: "Anytime" },
          ...(startTimeOptions || []),
        ];
        const newEndTimeOptions = [
          { value: ALL_OPTION, label: "Anytime" },
          ...(endTimeOptions || []),
        ];

        return {
          startTimeOptions: newStartTimeOptions,
          endTimeOptions: newEndTimeOptions,
        };
      });
    }
  }, [dispatch, response.data]);

  return response;
};
