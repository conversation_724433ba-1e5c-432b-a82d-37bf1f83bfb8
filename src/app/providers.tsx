"use client";

import { PropsWithChildren, useState } from "react";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { StoreProvider } from "./StoreContext";
import { ALL_OPTION } from "@/components/custom/activity-filters";
import { generateTimeOptions } from "@/common/common.utils";
import { AuthContextProvider } from "@/components/custom/login-auth/auth-provider";
import { FlagsProvider } from "@/components/custom/feature-flags/feature-flags";
import { useDefaultFlags } from "@/components/custom/feature-flags/flags";

const defaultEmbedConfigs = {
  sgt_color: "purple",
  accent_color: "#002966",
  button_color: "#0166ff",
};

export default function Provider({
  initialState = defaultEmbedConfigs,
  children,
}: PropsWithChildren<{ initialState?: Record<string, string> }>) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            staleTime: 30000,
            refetchInterval: 30 * 1000,
            refetchIntervalInBackground: true,
          },
        },
      })
  );

  // Call the hook here in the component body
  const flags = useDefaultFlags();

  return (
    <StoreProvider
      initialState={{
        shouldShowLoginModal: false,
        configs: { ...defaultEmbedConfigs, ...initialState },
        startTimeOptions: [
          { value: ALL_OPTION, label: "Anytime" },
          ...generateTimeOptions(),
        ],
        endTimeOptions: [
          { value: ALL_OPTION, label: "Anytime" },
          ...generateTimeOptions(),
        ],
      }}
    >
      <QueryClientProvider client={queryClient}>
        <FlagsProvider value={flags}>
          <AuthContextProvider>{children}</AuthContextProvider>
        </FlagsProvider>
        <ReactQueryDevtools />
      </QueryClientProvider>
    </StoreProvider>
  );
}
